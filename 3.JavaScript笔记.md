# JavaScript学习笔记

## 1. JavaScript基础概念

### 1.1 JavaScript简介

```javascript
// JavaScript是一种动态的、弱类型的编程语言
// 主要用于网页开发，现在也广泛用于服务器端开发

// 输出到控制台
console.log("Hello, JavaScript!"); // 最常用的调试方法
```

### 1.2 JavaScript的特点

- **解释型语言**：无需编译，直接执行
- **动态类型**：变量类型在运行时确定
- **事件驱动**：响应用户交互和系统事件
- **跨平台**：可在浏览器、服务器等多种环境运行

## 2. 变量和数据类型

### 2.1 变量声明

```javascript
// var（ES5及之前，函数作用域）
var name = "张三"; // 可重复声明，存在变量提升

// let（ES6+，块级作用域）
let age = 25; // 不可重复声明，更安全

// const（ES6+，常量）
const PI = 3.14159; // 必须初始化，不可修改
```

### 2.2 基本数据类型

```javascript
// 1. Number（数字）
let num1 = 42;        // 整数
let num2 = 3.14;      // 浮点数
let num3 = NaN;       // 非数字
let num4 = Infinity;  // 无穷大

// 2. String（字符串）
let str1 = "双引号字符串";
let str2 = '单引号字符串';
let str3 = `模板字符串，支持${name}变量插值`; // ES6+

// 3. Boolean（布尔）
let isTrue = true;
let isFalse = false;

// 4. Undefined（未定义）
let undefinedVar;
console.log(undefinedVar); // undefined

// 5. Null（空值）
let nullVar = null; // 表示空对象指针

// 6. Symbol（ES6+，唯一标识符）
let sym = Symbol("描述");

// 7. BigInt（ES2020+，大整数）
let bigNum = 1234567890123456789012345678901234567890n;
```

### 2.3 引用数据类型

```javascript
// Object（对象）
let person = {
    name: "李四",
    age: 30,
    isStudent: false
};

// Array（数组）
let fruits = ["苹果", "香蕉", "橙子"];
let numbers = [1, 2, 3, 4, 5];

// Function（函数）
function greet(name) {
    return `你好，${name}！`;
}
```

## 3. 运算符

### 3.1 算术运算符

```javascript
let a = 10, b = 3;

console.log(a + b);  // 13 加法
console.log(a - b);  // 7  减法
console.log(a * b);  // 30 乘法
console.log(a / b);  // 3.333... 除法
console.log(a % b);  // 1  取余
console.log(a ** b); // 1000 幂运算（ES2016+）

// 自增自减
a++; // 后自增
++a; // 前自增
b--; // 后自减
--b; // 前自减
```

### 3.2 比较运算符

```javascript
let x = 5, y = "5";

console.log(x == y);  // true  相等（类型转换）
console.log(x === y); // false 严格相等（不转换类型）
console.log(x != y);  // false 不等
console.log(x !== y); // true  严格不等
console.log(x > 3);   // true  大于
console.log(x >= 5);  // true  大于等于
console.log(x < 10);  // true  小于
console.log(x <= 5);  // true  小于等于
```

### 3.3 逻辑运算符

```javascript
let p = true, q = false;

console.log(p && q); // false 逻辑与（AND）
console.log(p || q); // true  逻辑或（OR）
console.log(!p);     // false 逻辑非（NOT）

// 短路求值
let result = p && "执行成功"; // 如果p为true，返回"执行成功"
let backup = q || "默认值";   // 如果q为false，返回"默认值"
```

## 4. 控制结构

### 4.1 条件语句

```javascript
// if-else语句
let score = 85;

if (score >= 90) {
    console.log("优秀");
} else if (score >= 80) {
    console.log("良好");
} else if (score >= 70) {
    console.log("中等");
} else if (score >= 60) {
    console.log("及格");
} else {
    console.log("不及格");
}

// 三元运算符
let result = score >= 60 ? "及格" : "不及格";

// switch语句
let day = 2;
switch (day) {
    case 1:
        console.log("周一");
        break;
    case 2:
        console.log("周二");
        break;
    case 3:
        console.log("周三");
        break;
    default:
        console.log("其他");
}
```

### 4.2 循环语句

```javascript
// for循环
for (let i = 0; i < 5; i++) {
    console.log(`第${i + 1}次循环`);
}

// while循环
let count = 0;
while (count < 3) {
    console.log(`count的值：${count}`);
    count++;
}

// do-while循环
let num = 0;
do {
    console.log(`num的值：${num}`);
    num++;
} while (num < 2);

// for...in循环（遍历对象属性；遍历内容：键名（key），适用对象：字典）
let obj = {name: "王五", age: 28, city: "北京"};
for (let key in obj) {
    console.log(`${key}: ${obj[key]}`);
}

// for...of循环（遍历可迭代对象；遍历内容：键值（value），适用对象：数组、字符串、Map、Set 等）
let arr = ["红", "绿", "蓝"];
for (let value of arr) {
    console.log(value);
}
```

## 5. 函数

### 5.1 函数声明和表达式

```javascript
// 函数声明（存在函数提升）
function add(a, b) {
    return a + b;
}

// 函数表达式
let multiply = function(a, b) {
    return a * b;
};

// 箭头函数（ES6+）
let subtract = (a, b) => a - b;

// 立即执行函数表达式（IIFE）
(function() {
    console.log("我立即执行了！");
})();
```

### 5.2 函数参数

```javascript
// 默认参数（ES6+）
function greet(name = "朋友") {
    return `你好，${name}！`;
}

// 剩余参数（ES6+）
function sum(...numbers) {
    return numbers.reduce((total, num) => total + num, 0);
}

// 参数解构
function createUser({name, age, email}) {
    return {
        name: name,
        age: age,
        email: email,
        id: Date.now()
    };
}

// 使用示例
console.log(greet());           // "你好，朋友！"
console.log(greet("小明"));     // "你好，小明！"
console.log(sum(1, 2, 3, 4));  // 10
```

### 5.3 高阶函数

```javascript
// 接受函数作为参数的函数
function executeOperation(a, b, operation) {
    return operation(a, b);
}

// 返回函数的函数
function createMultiplier(factor) {
    return function(number) {
        return number * factor;
    };
}

let double = createMultiplier(2);
console.log(double(5)); // 10

// 闭包示例
function counter() {
    let count = 0;
    return function() {
        return ++count;
    };
}

let myCounter = counter();
console.log(myCounter()); // 1
console.log(myCounter()); // 2
```

## 6. 对象和数组

### 6.1 对象操作

```javascript
// 创建对象
let student = {
    name: "赵六",
    age: 20,
    subjects: ["数学", "物理", "化学"],
    
    // 对象方法
    introduce: function() {
        return `我叫${this.name}，今年${this.age}岁`;
    },
    
    // ES6+简写方法
    study() {
        return `${this.name}正在学习`;
    }
};

// 访问属性
console.log(student.name);        // 点号访问
console.log(student["age"]);      // 方括号访问

// 添加/修改属性
student.grade = "大二";
student["gpa"] = 3.8;

// 删除属性
delete student.age;

// 检查属性是否存在
console.log("name" in student);           // true
console.log(student.hasOwnProperty("name")); // true

// 对象解构（ES6+）
let {name, subjects} = student;
console.log(name, subjects);
```

### 6.2 数组操作

```javascript
let fruits = ["苹果", "香蕉", "橙子"];

// 基本操作
fruits.push("葡萄");           // 末尾添加
fruits.unshift("草莓");        // 开头添加
let lastFruit = fruits.pop();  // 末尾删除并返回
let firstFruit = fruits.shift(); // 开头删除并返回

// 数组方法
let numbers = [1, 2, 3, 4, 5];

// map：转换数组元素
let doubled = numbers.map(num => num * 2);
console.log(doubled); // [2, 4, 6, 8, 10]

// filter：过滤数组元素
let evenNumbers = numbers.filter(num => num % 2 === 0);
console.log(evenNumbers); // [2, 4]

// reduce：累计计算
let total = numbers.reduce((sum, num) => sum + num, 0);
console.log(total); // 15

// find：查找第一个匹配元素
let found = numbers.find(num => num > 3);
console.log(found); // 4

// forEach：遍历数组
numbers.forEach((num, index) => {
    console.log(`索引${index}: ${num}`);
});

// 数组解构
let [first, second, ...rest] = numbers;
console.log(first, second, rest); // 1, 2, [3, 4, 5]
```

## 7. 异步编程

### 7.1 回调函数
```javascript
// 传统回调方式（容易产生回调地狱）
function fetchData(callback) {
    setTimeout(() => {
        callback("获取到的数据");
    }, 1000);
}

// 使用回调
fetchData(function(data) {
    console.log(data);
});

// 回调地狱示例
function getUserData(userId, callback) {
    // 模拟异步获取用户数据
    setTimeout(() => {
        console.log("获取用户数据...");
        callback(null, { id: userId, name: "张三" });
    }, 1000);
}

function getUserPosts(userId, callback) {
    // 模拟异步获取用户文章
    setTimeout(() => {
        console.log("获取用户文章...");
        callback(null, [
            { id: 1, title: "第一篇文章" },
            { id: 2, title: "第二篇文章" }
        ]);
    }, 800);
}

function getPostComments(postId, callback) {
    // 模拟异步获取文章评论
    setTimeout(() => {
        console.log("获取文章评论...");
        callback(null, [
            { id: 1, content: "很好的文章" },
            { id: 2, content: "受益匪浅" }
        ]);
    }, 600);
}

// 回调地狱 - 嵌套过深，难以维护
getUserData(123, (err, user) => {
    if (err) return console.error(err);
    
    getUserPosts(user.id, (err, posts) => {
        if (err) return console.error(err);
        
        getPostComments(posts[0].id, (err, comments) => {
            if (err) return console.error(err);
            
            console.log("用户：", user);
            console.log("文章：", posts[0]);
            console.log("评论：", comments);
        });
    });
});

// 使用命名函数改善回调地狱
function handleComments(err, comments) {
    if (err) return console.error(err);
    console.log("评论：", comments);
}

function handlePosts(user) {
    return function(err, posts) {
        if (err) return console.error(err);
        console.log("文章：", posts[0]);
        getPostComments(posts[0].id, handleComments);
    };
}

function handleUser(err, user) {
    if (err) return console.error(err);
    console.log("用户：", user);
    getUserPosts(user.id, handlePosts(user));
}

getUserData(123, handleUser);
```

### 7.2 Promise（承诺）

#### 什么是Promise？
Promise就像现实生活中的"承诺"一样。比如你向朋友承诺明天还钱，这个承诺有三种状态：
- **等待中**（pending）：还没到明天，承诺还在等待兑现
- **已兑现**（fulfilled）：明天到了，你成功还了钱
- **已拒绝**（rejected）：明天到了，但你没能还钱

#### 为什么需要Promise？
在没有Promise之前，处理异步操作（比如网络请求）会产生"回调地狱"：

```javascript
// 回调地狱的例子 - 难以阅读和维护
getUserInfo(userId, function(user) {
    getUserPosts(user.id, function(posts) {
        getPostComments(posts[0].id, function(comments) {
            // 嵌套太深，难以理解
            console.log("终于拿到评论了！", comments);
        });
    });
});
```

#### Promise的基本用法

```javascript
// 创建一个简单的Promise
let myPromise = new Promise((resolve, reject) => {
    // 模拟一个异步操作（比如网络请求）
    setTimeout(() => {
        let success = true; // 假设操作成功
        
        if (success) {
            resolve("数据获取成功！"); // 承诺兑现
        } else {
            reject("数据获取失败！"); // 承诺被拒绝
        }
    }, 2000); // 2秒后完成
});

// 使用Promise
myPromise
    .then(result => {
        console.log("成功：", result); // 处理成功的情况
    })
    .catch(error => {
        console.log("失败：", error); // 处理失败的情况
    })
    .finally(() => {
        console.log("无论成功失败都会执行这里");
    });
```

#### Promise链式调用
Promise最大的优势是可以链式调用，避免回调地狱：

```javascript
// 用Promise改写回调地狱
function getUserInfo(userId) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve({ id: userId, name: "张三" });
        }, 1000);
    });
}

function getUserPosts(userId) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve([{ id: 1, title: "我的第一篇文章" }]);
        }, 1000);
    });
}

// 链式调用 - 清晰易读
getUserInfo(123)
    .then(user => {
        console.log("用户信息：", user);
        return getUserPosts(user.id); // 返回新的Promise
    })
    .then(posts => {
        console.log("用户文章：", posts);
    })
    .catch(error => {
        console.log("出错了：", error);
    });
```

#### Promise的实用方法

```javascript
// Promise.all - 等待所有任务完成
let task1 = Promise.resolve("任务1完成");
let task2 = Promise.resolve("任务2完成");
let task3 = Promise.resolve("任务3完成");

Promise.all([task1, task2, task3])
    .then(results => {
        console.log("所有任务完成：", results);
        // 输出：["任务1完成", "任务2完成", "任务3完成"]
    });

// Promise.race - 谁先完成就用谁的结果
let fastTask = new Promise(resolve => 
    setTimeout(() => resolve("我最快！"), 100)
);
let slowTask = new Promise(resolve => 
    setTimeout(() => resolve("我比较慢"), 1000)
);

Promise.race([fastTask, slowTask])
    .then(result => {
        console.log("最快的结果：", result); // "我最快！"
    });
```

#### Promise的高级方法详解

```javascript
// 1. Promise.all - 并行执行所有Promise，只有全部成功才算成功
// 特点：快速失败（任何一个失败，整体就失败）
// 适用场景：需要所有数据都获取成功才能继续的情况
const promise1 = fetch('/api/user');        // 获取用户信息
const promise2 = fetch('/api/posts');       // 获取文章列表
const promise3 = fetch('/api/comments');    // 获取评论列表

Promise.all([promise1, promise2, promise3])
    .then(responses => {
        console.log('所有请求都成功了');
        // 注意：这里的responses是Response对象数组，需要进一步解析
        // 再次使用Promise.all来并行解析所有响应的JSON数据
        return Promise.all(responses.map(response => response.json()));
    })
    .then(data => {
        // 解构赋值获取各个API的数据，顺序与传入Promise.all的数组顺序一致
        const [userData, postsData, commentsData] = data;
        console.log('用户数据:', userData);
        console.log('文章数据:', postsData);
        console.log('评论数据:', commentsData);
        
        // 现在可以使用所有数据进行后续处理
        return { user: userData, posts: postsData, comments: commentsData };
    })
    .catch(error => {
        // 只要有一个请求失败，就会进入这里
        console.log('至少有一个请求失败了:', error);
        // 可以根据需要提供默认数据或重试逻辑
    });

// 2. Promise.allSettled - 等待所有Promise完成，无论成功还是失败
// 特点：永远不会失败，会等待所有Promise完成
// 适用场景：希望获取所有结果，即使部分失败也要继续处理的情况
const promises = [
    fetch('/api/user'),           // 正常的API请求
    fetch('/api/posts'),          // 正常的API请求  
    fetch('/api/invalid-url')     // 故意写错的URL，会失败
];

Promise.allSettled(promises)
    .then(results => {
        // results数组包含所有Promise的结果，无论成功失败
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                // 成功的Promise，result.value包含实际结果
                console.log(`Promise ${index} 成功:`, result.value);
            } else {
                // 失败的Promise，result.reason包含错误信息
                console.log(`Promise ${index} 失败:`, result.reason);
            }
        });
        
        // 可以分别处理成功和失败的结果
        const successful = results.filter(r => r.status === 'fulfilled');
        const failed = results.filter(r => r.status === 'rejected');
        
        console.log(`成功: ${successful.length}个, 失败: ${failed.length}个`);
    });

// 3. Promise.race - 竞速执行，谁先完成（成功或失败）就用谁的结果
// 特点：只关心最快的那个结果
// 适用场景：超时控制、多个数据源选择最快的等
const timeout = new Promise((_, reject) => 
    // 创建一个5秒后会失败的Promise作为超时控制
    setTimeout(() => reject(new Error('请求超时')), 5000)
);

const dataRequest = fetch('/api/data');  // 实际的数据请求

// 让数据请求和超时Promise竞速
Promise.race([dataRequest, timeout])
    .then(response => {
        // 如果数据请求在5秒内完成，会执行这里
        console.log('在超时前获取到数据');
        return response.json();
    })
    .catch(error => {
        // 如果超时或请求失败，会执行这里
        if (error.message === '请求超时') {
            console.log('请求超时了，可以显示缓存数据或提示用户');
        } else {
            console.log('请求失败:', error);
        }
    });

// 4. Promise.any - 任意一个成功就算成功（ES2021+新特性）
// 特点：只要有一个成功就成功，所有都失败才失败
// 适用场景：多个备用服务器，任何一个响应就可以
const servers = [
    fetch('https://server1.com/api'),    // 主服务器
    fetch('https://server2.com/api'),    // 备用服务器1
    fetch('https://server3.com/api')     // 备用服务器2
];

Promise.any(servers)
    .then(response => {
        // 只要有一个服务器成功响应，就会执行这里
        console.log('至少有一个服务器响应了');
        return response.json();
    })
    .catch(error => {
        // 只有当所有服务器都失败时，才会执行这里
        // error是一个AggregateError，包含所有失败的原因
        console.log('所有服务器都失败了:', error);
        console.log('失败详情:', error.errors); // 查看每个服务器的具体失败原因
    });

// 5. Promise.resolve 和 Promise.reject - 创建立即解决的Promise
// 用途：将普通值包装成Promise，或创建立即失败的Promise
const immediateSuccess = Promise.resolve('立即成功的数据');
const immediateFailure = Promise.reject('立即失败的错误');

// Promise.resolve的常见用途：统一处理可能是Promise也可能是普通值的情况
function processValue(value) {
    // 无论value是普通值还是Promise，都统一转换为Promise处理
    return Promise.resolve(value)
        .then(val => {
            console.log('处理值:', val);
            return val * 2;  // 对值进行处理
        });
}

// 使用示例
processValue(5);                    // 处理普通值
processValue(Promise.resolve(10));  // 处理Promise值

// Promise.reject的用途：在某些条件下立即创建失败的Promise
function validateAge(age) {
    if (age < 0) {
        return Promise.reject(new Error('年龄不能为负数'));
    }
    if (age > 150) {
        return Promise.reject(new Error('年龄不能超过150岁'));
    }
    return Promise.resolve(age);  // 验证通过，返回成功的Promise
}

processValue(5); // 处理普通值
processValue(Promise.resolve(10)); // 处理Promise值
```

#### Promise链式调用的高级技巧

```javascript
// 1. 链式调用中的错误处理和恢复
// 演示如何在Promise链中优雅地处理错误并提供备用方案
function fetchUserProfile(userId) {
    return fetch(`/api/users/${userId}`)
        .then(response => {
            // 检查HTTP响应状态，如果不是成功状态就抛出错误
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();  // 解析JSON数据
        })
        .then(user => {
            // 数据获取成功，记录日志并返回用户数据
            console.log('获取用户信息成功:', user.name);
            return user;
        })
        .catch(error => {
            // 捕获任何错误（网络错误、HTTP错误、JSON解析错误等）
            console.error('获取用户信息失败:', error);
            
            // 提供默认的用户信息作为备用方案，确保程序能继续运行
            return { 
                id: userId, 
                name: '未知用户', 
                email: '',
                isDefault: true  // 标记这是默认数据
            };
        });
}

// 2. 条件性链式调用 - 根据条件决定是否执行额外的异步操作
function processUserData(userId, includeDetails = false) {
    return fetchUserProfile(userId)
        .then(user => {
            // 根据参数决定是否需要获取详细信息
            if (includeDetails && !user.isDefault) {
                // 只有在用户数据不是默认数据时才获取详细信息
                console.log('获取用户详细信息...');
                return fetch(`/api/users/${userId}/details`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('详细信息获取失败');
                        }
                        return response.json();
                    })
                    .then(details => {
                        // 合并基本信息和详细信息
                        return { ...user, ...details, hasDetails: true };
                    })
                    .catch(error => {
                        // 如果详细信息获取失败，仍然返回基本信息
                        console.warn('详细信息获取失败，使用基本信息:', error);
                        return { ...user, hasDetails: false };
                    });
            }
            // 不需要详细信息，直接返回基本用户信息
            return { ...user, hasDetails: false };
        })
        .then(userData => {
            console.log('最终用户数据:', userData);
            return userData;
        });
}

// 3. 动态链式调用 - 根据数组配置动态构建Promise链
// 这种模式适用于需要按顺序执行多个异步操作的场景
function processDataPipeline(data, operations) {
    // 使用reduce方法将操作数组转换为Promise链
    return operations.reduce((promise, operation) => {
        return promise.then(result => {
            console.log(`开始执行操作: ${operation.name}`);
            console.log(`当前数据:`, result);
            
            // 执行当前操作，可能返回Promise或普通值
            const operationResult = operation.fn(result);
            
            // 确保返回Promise，以便链式调用
            return Promise.resolve(operationResult)
                .then(newResult => {
                    console.log(`操作 ${operation.name} 完成`);
                    return newResult;
                })
                .catch(error => {
                    // 为每个操作提供错误处理
                    console.error(`操作 ${operation.name} 失败:`, error);
                    
                    // 根据操作的重要性决定是否继续
                    if (operation.critical) {
                        throw error;  // 关键操作失败，中断整个流程
                    } else {
                        return result;  // 非关键操作失败，使用上一步的结果继续
                    }
                });
        });
    }, Promise.resolve(data));  // 初始Promise，包含原始数据
}

// 使用示例 - 定义数据处理流水线
const operations = [
    { 
        name: '验证数据', 
        fn: data => {
            if (!data || typeof data !== 'object') {
                throw new Error('数据格式无效');
            }
            return { ...data, validated: true };
        },
        critical: true  // 关键操作，失败则中断
    },
    { 
        name: '转换格式', 
        fn: data => {
            // 模拟异步转换操作
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve({ ...data, transformed: true, timestamp: Date.now() });
                }, 500);
            });
        },
        critical: false
    },
    { 
        name: '保存数据', 
        fn: data => {
            // 模拟保存到数据库
            console.log('保存数据到数据库...');
            return { ...data, saved: true, id: Math.random().toString(36) };
        },
        critical: true
    }
];

// 执行数据处理流水线
const rawData = { name: '测试数据', value: 123 };
processDataPipeline(rawData, operations)
    .then(result => {
        console.log('所有操作完成，最终结果:', result);
    })
    .catch(error => {
        console.error('流水线处理失败:', error);
    });
```

#### Promise的实用工具函数

```javascript
// 1. 延迟函数 - 创建一个在指定时间后解决的Promise
// 用途：在异步操作之间添加延迟，模拟网络延迟，实现定时任务等
function delay(ms) {
    return new Promise(resolve => {
        // 使用setTimeout在指定毫秒后调用resolve
        setTimeout(resolve, ms);
    });
}

// 使用延迟函数的示例
console.log('开始执行');
delay(2000)  // 等待2秒
    .then(() => {
        console.log('2秒后执行这里');
        return delay(1000);  // 可以链式调用，再等待1秒
    })
    .then(() => {
        console.log('又过了1秒，总共3秒');
    });

// 在async/await中使用延迟
async function delayedOperation() {
    console.log('操作开始');
    await delay(1000);  // 等待1秒
    console.log('1秒后继续');
    await delay(500);   // 再等待0.5秒
    console.log('操作完成');
}

// 2. 重试函数 - 失败时自动重试的Promise包装器
// 参数说明：
// - fn: 要执行的函数，必须返回Promise
// - maxAttempts: 最大尝试次数（包括第一次）
// - delayMs: 重试间隔时间（毫秒）
function retry(fn, maxAttempts = 3, delayMs = 1000) {
    return new Promise((resolve, reject) => {
        let attempts = 0;  // 当前尝试次数
        
        function attempt() {
            attempts++;
            console.log(`第${attempts}次尝试...`);
            
            // 执行传入的函数
            fn()
                .then(result => {
                    // 成功时直接resolve
                    console.log(`第${attempts}次尝试成功`);
                    resolve(result);
                })
                .catch(error => {
                    console.log(`第${attempts}次尝试失败:`, error.message);
                    
                    if (attempts >= maxAttempts) {
                        // 达到最大重试次数，最终失败
                        reject(new Error(`${maxAttempts}次重试后仍然失败: ${error.message}`));
                    } else {
                        // 还有重试机会，延迟后再次尝试
                        console.log(`${delayMs}ms后进行第${attempts + 1}次重试...`);
                        setTimeout(attempt, delayMs);
                    }
                });
        }
        
        attempt();  // 开始第一次尝试
    });
}

// 使用重试函数的示例
retry(
    () => {
        // 模拟一个不稳定的API请求，70%概率失败
        return new Promise((resolve, reject) => {
            if (Math.random() > 0.3) {
                reject(new Error('网络不稳定'));
            } else {
                resolve('请求成功的数据');
            }
        });
    }, 
    5,      // 最多重试5次
    2000    // 每次重试间隔2秒
)
.then(data => console.log('最终获取到数据:', data))
.catch(error => console.error('所有重试都失败了:', error));

// 3. 超时函数 - 为Promise添加超时限制
// 如果Promise在指定时间内没有完成，就会被拒绝
function withTimeout(promise, timeoutMs) {
    // 创建一个超时Promise，在指定时间后reject
    const timeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('操作超时')), timeoutMs)
    );
    
    // 使用Promise.race让原Promise和超时Promise竞速
    // 谁先完成就用谁的结果
    return Promise.race([promise, timeout]);
}

// 使用超时函数的示例
const slowRequest = new Promise(resolve => {
    // 模拟一个需要8秒才能完成的请求
    setTimeout(() => resolve('慢请求的数据'), 8000);
});

withTimeout(slowRequest, 5000)  // 设置5秒超时
    .then(data => console.log('在超时前获取到数据:', data))
    .catch(error => {
        if (error.message === '操作超时') {
            console.log('请求超时，请稍后重试');
            // 可以在这里显示超时提示给用户
        } else {
            console.error('请求失败:', error);
        }
    });

// 4. 批量处理函数 - 将大量任务分批串行处理，避免并发过多
// 参数说明：
// - items: 要处理的项目数组
// - processor: 处理单个项目的函数，返回Promise
// - batchSize: 每批处理的项目数量
function batchProcess(items, processor, batchSize = 3) {
    // 将items数组分割成多个批次
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
        batches.push(items.slice(i, i + batchSize));
    }
    
    console.log(`总共${items.length}个项目，分为${batches.length}个批次处理`);
    
    // 使用reduce串行处理每个批次
    return batches.reduce((promise, batch, batchIndex) => {
        return promise.then(results => {
            console.log(`开始处理第${batchIndex + 1}批次: ${batch.length}个项目`);
            
            // 并行处理当前批次的所有项目
            return Promise.all(batch.map((item, itemIndex) => {
                console.log(`  处理项目 ${batchIndex * batchSize + itemIndex + 1}: ${item}`);
                return processor(item);
            }))
            .then(batchResults => {
                console.log(`第${batchIndex + 1}批次处理完成`);
                // 将当前批次的结果合并到总结果中
                return [...results, ...batchResults];
            });
        });
    }, Promise.resolve([]));  // 初始值为空数组
}

// 使用批量处理函数的示例
const userIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// 模拟处理单个用户的函数
function processUser(userId) {
    return new Promise(resolve => {
        // 模拟异步处理，随机延迟1-3秒
        const delay = Math.random() * 2000 + 1000;
        setTimeout(() => {
            resolve(`用户${userId}的处理结果`);
        }, delay);
    });
}

batchProcess(userIds, processUser, 3)  // 每批处理3个用户
    .then(allResults => {
        console.log('所有用户处理完成:');
        allResults.forEach((result, index) => {
            console.log(`  ${index + 1}. ${result}`);
        });
    })
    .catch(error => console.error('批量处理失败:', error));

// 使用批量处理
const userIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
batchProcess(userIds, id => fetchUserProfile(id), 3)
    .then(allUsers => console.log('所有用户数据:', allUsers))
    .catch(error => console.error('批量处理失败:', error));

// 5. 缓存Promise结果
class PromiseCache {
    constructor() {
        this.cache = new Map();
    }
    
    get(key, promiseFactory) {
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const promise = promiseFactory()
            .catch(error => {
                // 如果失败，从缓存中移除
                this.cache.delete(key);
                throw error;
            });
        
        this.cache.set(key, promise);
        return promise;
    }
    
    clear(key) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }
}

const cache = new PromiseCache();

function getCachedUserData(userId) {
    return cache.get(`user-${userId}`, () => fetchUserProfile(userId));
}

// 6. Promise队列
class PromiseQueue {
    constructor(concurrency = 1) {
        this.concurrency = concurrency;
        this.running = 0;
        this.queue = [];
    }
    
    add(promiseFactory) {
        return new Promise((resolve, reject) => {
            this.queue.push({
                promiseFactory,
                resolve,
                reject
            });
            this.process();
        });
    }
    
    async process() {
        if (this.running >= this.concurrency || this.queue.length === 0) {
            return;
        }
        
        this.running++;
        const { promiseFactory, resolve, reject } = this.queue.shift();
        
        try {
            const result = await promiseFactory();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            this.process();
        }
    }
}

const queue = new PromiseQueue(2); // 最多同时执行2个Promise

// 添加任务到队列
for (let i = 1; i <= 10; i++) {
    queue.add(() => {
        console.log(`开始任务 ${i}`);
        return delay(1000).then(() => {
            console.log(`完成任务 ${i}`);
            return `结果 ${i}`;
        });
    });
}
```

#### Promise的错误处理最佳实践

```javascript
// 1. 全局错误处理
window.addEventListener('unhandledrejection', event => {
    console.error('未处理的Promise拒绝:', event.reason);
    // 可以在这里记录错误日志
    logError(event.reason);
    
    // 阻止默认的错误处理
    event.preventDefault();
});

// 2. 错误分类处理
class NetworkError extends Error {
    constructor(message, status) {
        super(message);
        this.name = 'NetworkError';
        this.status = status;
    }
}

class ValidationError extends Error {
    constructor(message, field) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
    }
}

function handleApiRequest(url, data) {
    return fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new NetworkError(`请求失败: ${response.statusText}`, response.status);
        }
        return response.json();
    })
    .then(result => {
        if (!result.valid) {
            throw new ValidationError('数据验证失败', result.field);
        }
        return result;
    })
    .catch(error => {
        if (error instanceof NetworkError) {
            console.error('网络错误:', error.message, '状态码:', error.status);
            if (error.status >= 500) {
                // 服务器错误，可以重试
                return retry(() => handleApiRequest(url, data));
            }
        } else if (error instanceof ValidationError) {
            console.error('验证错误:', error.message, '字段:', error.field);
            // 显示用户友好的错误信息
            showValidationError(error.field, error.message);
        } else {
            console.error('未知错误:', error);
        }
        throw error;
    });
}

// 3. Promise链中的错误恢复
function robustDataProcessing(data) {
    return Promise.resolve(data)
        .then(validateData)
        .catch(error => {
            console.warn('数据验证失败，使用默认数据:', error);
            return getDefaultData();
        })
        .then(processData)
        .catch(error => {
            console.warn('数据处理失败，尝试简化处理:', error);
            return simpleProcessData(data);
        })
        .then(saveData)
        .catch(error => {
            console.error('数据保存失败:', error);
            // 保存到本地存储作为备份
            return saveToLocalStorage(data);
        });
}
```

#### 实际应用场景

```javascript
// 场景1：用户注册流程
function registerUser(userData) {
    return validateUserData(userData)
        .then(validData => {
            console.log('数据验证通过');
            return checkUserExists(validData.email);
        })
        .then(exists => {
            if (exists) {
                throw new Error('用户已存在');
            }
            return createUser(userData);
        })
        .then(user => {
            console.log('用户创建成功');
            return sendWelcomeEmail(user.email);
        })
        .then(() => {
            console.log('欢迎邮件发送成功');
            return { success: true, message: '注册成功' };
        })
        .catch(error => {
            console.error('注册失败:', error);
            return { success: false, message: error.message };
        });
}

// 场景2：文件上传进度
function uploadFileWithProgress(file, onProgress) {
    return new Promise((resolve, reject) => {
        const formData = new FormData();
        formData.append('file', file);
        
        const xhr = new XMLHttpRequest();
        
        xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
                const progress = (event.loaded / event.total) * 100;
                onProgress(progress);
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                resolve(JSON.parse(xhr.responseText));
            } else {
                reject(new Error(`上传失败: ${xhr.statusText}`));
            }
        });
        
        xhr.addEventListener('error', () => {
            reject(new Error('网络错误'));
        });
        
        xhr.open('POST', '/api/upload');
        xhr.send(formData);
    });
}

// 使用文件上传
const fileInput = document.getElementById('fileInput');
fileInput.addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (file) {
        uploadFileWithProgress(file, (progress) => {
            console.log(`上传进度: ${progress.toFixed(2)}%`);
        })
        .then(result => {
            console.log('上传成功:', result);
        })
        .catch(error => {
            console.error('上传失败:', error);
        });
    }
});

// 场景3：数据预加载和缓存
class DataPreloader {
    constructor() {
        this.cache = new Map();
        this.preloadPromises = new Map();
    }
    
    preload(key, dataLoader) {
        if (this.preloadPromises.has(key)) {
            return this.preloadPromises.get(key);
        }
        
        const promise = dataLoader()
            .then(data => {
                this.cache.set(key, data);
                this.preloadPromises.delete(key);
                return data;
            })
            .catch(error => {
                this.preloadPromises.delete(key);
                throw error;
            });
        
        this.preloadPromises.set(key, promise);
        return promise;
    }
    
    get(key) {
        if (this.cache.has(key)) {
            return Promise.resolve(this.cache.get(key));
        }
        
        if (this.preloadPromises.has(key)) {
            return this.preloadPromises.get(key);
        }
        
        return Promise.reject(new Error(`数据 ${key} 未预加载`));
    }
}

const preloader = new DataPreloader();

// 预加载关键数据
preloader.preload('userProfile', () => fetch('/api/user').then(r => r.json()));
preloader.preload('appConfig', () => fetch('/api/config').then(r => r.json()));

// 在需要时快速获取
preloader.get('userProfile')
    .then(profile => console.log('用户资料:', profile))
    .catch(error => console.error('获取用户资料失败:', error));
```

### 7.3 Async/Await（异步等待）

#### 什么是Async/Await？
Async/Await是Promise的"语法糖"，让异步代码看起来像同步代码一样，更容易理解和编写。

想象一下：

- **async**：告诉JavaScript"这个函数里有异步操作"
- **await**：告诉JavaScript"等一下，等这个异步操作完成再继续"

#### Promise vs Async/Await 对比

```javascript
// 使用Promise的方式
function getUserDataWithPromise() {
    return getUserInfo(123)
        .then(user => {
            console.log("用户信息：", user);
            return getUserPosts(user.id);
        })
        .then(posts => {
            console.log("用户文章：", posts);
            return posts;
        })
        .catch(error => {
            console.log("出错了：", error);
        });
}

// 使用Async/Await的方式 - 更直观
async function getUserDataWithAsync() {
    try {
        const user = await getUserInfo(123);
        console.log("用户信息：", user);
        
        const posts = await getUserPosts(user.id);
        console.log("用户文章：", posts);
        
        return posts;
    } catch (error) {
        console.log("出错了：", error);
    }
}
```

#### 基本用法

```javascript
// 1. 声明async函数
async function fetchData() {
    // 模拟网络请求
    const response = await new Promise(resolve => {
        setTimeout(() => {
            resolve("数据获取成功！");
        }, 2000);
    });
    
    console.log(response);
    return response;
}

// 2. 调用async函数
fetchData(); // 函数会立即返回一个Promise

// 3. 也可以这样调用
fetchData().then(result => {
    console.log("最终结果：", result);
});
```

#### 错误处理

```javascript
// 使用try-catch处理错误
async function safeDataFetch() {
    try {
        const userData = await fetchUserData();
        const userPosts = await fetchUserPosts(userData.id);
        
        console.log("用户和文章数据都获取成功！");
        return { userData, userPosts };
        
    } catch (error) {
        console.log("获取数据时出错：", error);
        
        // 可以返回默认值
        return {
            userData: { name: "未知用户" },
            userPosts: []
        };
    }
}
```

#### 并行 vs 串行执行

```javascript
// 串行执行（一个接一个）- 比较慢
async function serialExecution() {
    console.log("开始串行执行...");
    
    const task1 = await doTask1(); // 等待1秒
    const task2 = await doTask2(); // 再等待1秒
    const task3 = await doTask3(); // 再等待1秒
    
    console.log("串行执行完成，总共用时3秒");
    return [task1, task2, task3];
}

// 并行执行（同时进行）- 比较快
async function parallelExecution() {
    console.log("开始并行执行...");
    
    // 同时启动所有任务
    const [task1, task2, task3] = await Promise.all([
        doTask1(), // 同时开始
        doTask2(), // 同时开始
        doTask3()  // 同时开始
    ]);
    
    console.log("并行执行完成，总共用时1秒");
    return [task1, task2, task3];
}
```

#### 实际应用场景

```javascript
// 场景1：获取用户信息和设置
async function loadUserDashboard(userId) {
    try {
        // 同时获取用户信息和设置（并行）
        const [userInfo, userSettings] = await Promise.all([
            fetchUserInfo(userId),
            fetchUserSettings(userId)
        ]);
        
        console.log("用户信息：", userInfo);
        console.log("用户设置：", userSettings);
        
        // 基于用户信息获取个性化内容（串行）
        const personalizedContent = await fetchPersonalizedContent(userInfo.preferences);
        
        return {
            userInfo,
            userSettings,
            personalizedContent
        };
        
    } catch (error) {
        console.log("加载用户面板失败：", error);
        throw error;
    }
}

// 场景2：文件上传
async function uploadFile(file) {
    try {
        console.log("开始上传文件...");
        
        // 第一步：获取上传许可
        const uploadPermission = await requestUploadPermission();
        
        // 第二步：上传文件
        const uploadResult = await uploadToServer(file, uploadPermission.token);
        
        // 第三步：通知服务器上传完成
        const confirmResult = await confirmUpload(uploadResult.fileId);
        
        console.log("文件上传成功！");
        return confirmResult;
        
    } catch (error) {
        console.log("文件上传失败：", error);
        throw error;
    }
}

// 使用示例
async function main() {
    try {
        const dashboard = await loadUserDashboard(123);
        console.log("用户面板加载完成：", dashboard);
    } catch (error) {
        console.log("应用启动失败：", error);
    }
}

main(); // 启动应用
```

#### 常见注意事项

```javascript
// ❌ 错误：忘记使用await
async function wrongWay() {
    const result = fetchData(); // 这里得到的是Promise，不是数据
    console.log(result); // 输出：Promise对象
}

// ✅ 正确：使用await等待结果
async function rightWay() {
    const result = await fetchData(); // 等待获取实际数据
    console.log(result); // 输出：实际的数据
}

// ❌ 错误：在普通函数中使用await
function normalFunction() {
    const result = await fetchData(); // 语法错误！
}

// ✅ 正确：只能在async函数中使用await
async function asyncFunction() {
    const result = await fetchData(); // 正确
}
```

#### Await的高级用法和最佳实践

```javascript
// 1. 条件性await - 根据条件决定是否等待异步操作
// 适用场景：根据用户权限、配置选项等条件决定是否执行耗时操作
async function conditionalAwait(shouldWait, useCache = false) {
    let result;
    
    if (shouldWait) {
        console.log('条件满足，开始异步获取数据...');
        result = await fetchData(); // 只在需要时等待异步操作
    } else if (useCache) {
        console.log('使用缓存数据...');
        result = getCachedData(); // 同步获取缓存数据
    } else {
        console.log('使用默认值...');
        result = "默认值"; // 直接使用默认值
    }
    
    return result;
}

// 使用示例
const userHasPremium = true;
const data = await conditionalAwait(userHasPremium, true);

// 2. 循环中的await - 串行vs并行处理的正确方式
async function processItems(items) {
    console.log(`需要处理${items.length}个项目`);
    
    // ❌ 错误方式：串行处理，每个项目依次等待，总时间 = 所有项目时间之和
    console.log("=== 串行处理开始 ===");
    const startTime = Date.now();
    
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        console.log(`开始处理项目 ${i + 1}: ${item}`);
        const result = await processItem(item); // 等待当前项目完成才处理下一个
        console.log(`项目 ${i + 1} 处理完成: ${result}`);
    }
    
    const serialTime = Date.now() - startTime;
    console.log(`串行处理总耗时: ${serialTime}ms`);
    
    // ✅ 正确方式：并行处理，所有项目同时开始，总时间 ≈ 最慢项目的时间
    console.log("=== 并行处理开始 ===");
    const parallelStartTime = Date.now();
    
    // 先创建所有Promise，但不等待
    const promises = items.map((item, index) => {
        console.log(`启动项目 ${index + 1}: ${item}`);
        return processItem(item);
    });
    
    // 一次性等待所有Promise完成
    const results = await Promise.all(promises);
    
    // 处理结果
    results.forEach((result, index) => {
        console.log(`项目 ${index + 1} 处理完成: ${result}`);
    });
    
    const parallelTime = Date.now() - parallelStartTime;
    console.log(`并行处理总耗时: ${parallelTime}ms`);
    console.log(`性能提升: ${((serialTime - parallelTime) / serialTime * 100).toFixed(1)}%`);
    
    return results;
}

// 模拟处理单个项目的异步函数
async function processItem(item) {
    const processingTime = Math.random() * 2000 + 1000; // 1-3秒随机处理时间
    await new Promise(resolve => setTimeout(resolve, processingTime));
    return `${item}_processed`;
}

// 3. 带超时的await - 防止请求无限等待
// 适用场景：网络请求、文件操作等可能长时间无响应的操作
async function fetchWithTimeout(url, timeout = 5000) {
    console.log(`开始请求: ${url}，超时时间: ${timeout}ms`);
    
    // 创建超时Promise，在指定时间后reject
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            reject(new Error('请求超时'));
        }, timeout);
    });
    
    // 创建实际的请求Promise
    const fetchPromise = fetch(url).then(response => {
        console.log(`请求响应状态: ${response.status}`);
        return response;
    });
    
    try {
        // 使用Promise.race让请求和超时竞速
        // 谁先完成（成功或失败）就用谁的结果
        const response = await Promise.race([fetchPromise, timeoutPromise]);
        
        // 如果请求成功，解析JSON数据
        const data = await response.json();
        console.log('数据获取成功');
        return data;
    } catch (error) {
        if (error.message === '请求超时') {
            console.log('请求超时，尝试使用缓存数据');
            // 超时时的降级策略：使用缓存数据
            return getCachedData(url);
        } else {
            console.error('请求失败:', error.message);
            throw error; // 重新抛出非超时错误
        }
    }
}

// 模拟获取缓存数据的函数
function getCachedData(url) {
    console.log(`从缓存获取数据: ${url}`);
    return { cached: true, data: '缓存的数据', timestamp: Date.now() };
}

// 4. 重试机制的await - 失败后自动重试，支持指数退避
// 适用场景：网络不稳定、服务器临时故障等需要重试的情况
async function fetchWithRetry(url, maxRetries = 3) {
    console.log(`开始请求 ${url}，最大重试次数: ${maxRetries}`);
    
    // 循环进行重试
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            console.log(`第${attempt + 1}次尝试...`);
            
            const response = await fetch(url);
            
            // 检查响应状态
            if (response.ok) {
                console.log(`第${attempt + 1}次尝试成功`);
                return await response.json();
            } else {
                // HTTP错误状态码也算失败
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.log(`第${attempt + 1}次尝试失败: ${error.message}`);
            
            // 如果是最后一次尝试，不再重试
            if (attempt === maxRetries - 1) {
                console.error(`${maxRetries}次重试后仍然失败`);
                throw new Error(`${maxRetries}次重试后仍然失败: ${error.message}`);
            }
            
            // 指数退避策略：每次重试的等待时间翻倍
            // 第1次重试等1秒，第2次等2秒，第3次等4秒...
            const delay = Math.pow(2, attempt) * 1000;
            console.log(`${delay}ms后进行第${attempt + 2}次重试...`);
            
            // 等待指定时间后再重试
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}

// 5. 限制并发数量的await - 控制同时执行的任务数量，避免系统过载
// 适用场景：大量API请求、文件处理、数据库操作等需要控制并发的场景
async function limitedConcurrency(tasks, limit = 3) {
    console.log(`开始处理${tasks.length}个任务，并发限制: ${limit}`);
    
    const results = [];      // 存储所有任务的Promise
    const executing = [];    // 存储当前正在执行的Promise
    
    for (let i = 0; i < tasks.length; i++) {
        const task = tasks[i];
        
        // 创建任务Promise，并在完成时从executing数组中移除
        const promise = task().then(result => {
            console.log(`任务 ${i + 1} 完成`);
            // 从正在执行的数组中移除已完成的Promise
            const index = executing.indexOf(promise);
            if (index > -1) {
                executing.splice(index, 1);
            }
            return result;
        }).catch(error => {
            console.error(`任务 ${i + 1} 失败:`, error);
            // 即使失败也要从executing数组中移除
            const index = executing.indexOf(promise);
            if (index > -1) {
                executing.splice(index, 1);
            }
            throw error;
        });
        
        console.log(`启动任务 ${i + 1}`);
        results.push(promise);      // 添加到结果数组
        executing.push(promise);    // 添加到执行中数组
        
        // 如果达到并发限制，等待至少一个任务完成
        if (executing.length >= limit) {
            console.log(`达到并发限制(${limit})，等待任务完成...`);
            // Promise.race返回最先完成的Promise
            await Promise.race(executing);
        }
    }
    
    // 等待所有任务完成
    console.log('等待所有任务完成...');
    return Promise.all(results);
}

// 使用示例 - 模拟大量API请求
const tasks = [
    () => simulateApiCall('用户数据', 1000),
    () => simulateApiCall('订单数据', 1500),
    () => simulateApiCall('产品数据', 800),
    () => simulateApiCall('库存数据', 1200),
    () => simulateApiCall('统计数据', 2000)
];

// 模拟API调用
async function simulateApiCall(dataType, delay) {
    console.log(`  开始获取${dataType}...`);
    await new Promise(resolve => setTimeout(resolve, delay));
    return `${dataType}获取完成`;
}

// 执行限制并发的任务
limitedConcurrency(tasks, 2)  // 最多同时执行2个任务
    .then(results => {
        console.log('所有任务完成:', results);
    })
    .catch(error => {
        console.error('任务执行失败:', error);
    });

// 6. 缓存异步结果 - 避免重复请求，提高性能
// 适用场景：频繁请求相同数据、计算结果缓存等
class AsyncCache {
    constructor(ttl = 5 * 60 * 1000) { // 默认5分钟过期
        this.cache = new Map();  // 存储缓存数据
        this.ttl = ttl;         // 缓存过期时间（毫秒）
    }
    
    async get(key, fetcher) {
        console.log(`尝试获取缓存: ${key}`);
        
        // 检查缓存是否存在且未过期
        if (this.cache.has(key)) {
            const cached = this.cache.get(key);
            
            // 检查是否过期
            if (Date.now() - cached.timestamp < this.ttl) {
                console.log(`缓存命中: ${key}`);
                
                // 如果缓存的是Promise，直接返回
                if (cached.data instanceof Promise) {
                    return cached.data;
                }
                // 如果缓存的是结果，包装成Promise返回
                return Promise.resolve(cached.data);
            } else {
                console.log(`缓存过期，删除: ${key}`);
                this.cache.delete(key);
            }
        }
        
        console.log(`缓存未命中，开始获取数据: ${key}`);
        
        // 执行fetcher函数获取数据
        const promise = fetcher();
        
        // 立即将Promise存入缓存，避免重复请求
        this.cache.set(key, {
            data: promise,
            timestamp: Date.now()
        });
        
        try {
            // 等待数据获取完成
            const result = await promise;
            console.log(`数据获取成功，更新缓存: ${key}`);
            
            // 将实际结果存入缓存
            this.cache.set(key, {
                data: result,
                timestamp: Date.now()
            });
            
            return result;
        } catch (error) {
            console.error(`数据获取失败，删除缓存: ${key}`, error);
            // 如果获取失败，从缓存中移除
            this.cache.delete(key);
            throw error;
        }
    }
    
    // 手动清除缓存
    clear(key) {
        if (key) {
            console.log(`手动清除缓存: ${key}`);
            this.cache.delete(key);
        } else {
            console.log('清除所有缓存');
            this.cache.clear();
        }
    }
    
    // 获取缓存统计信息
    getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 创建缓存实例
const cache = new AsyncCache(10 * 60 * 1000); // 10分钟过期

// 使用缓存的函数
async function getCachedUserData(userId) {
    return cache.get(`user-${userId}`, async () => {
        // 这个函数只有在缓存未命中时才会执行
        console.log(`从API获取用户${userId}的数据...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟网络延迟
        return {
            id: userId,
            name: `用户${userId}`,
            email: `user${userId}@example.com`,
            fetchTime: new Date().toISOString()
        };
    });
}

// 使用示例
async function testCache() {
    console.log('=== 缓存测试开始 ===');
    
    // 第一次请求 - 缓存未命中
    const user1 = await getCachedUserData(123);
    console.log('第一次请求结果:', user1);
    
    // 第二次请求相同数据 - 缓存命中
    const user2 = await getCachedUserData(123);
    console.log('第二次请求结果:', user2);
    
    // 请求不同数据 - 缓存未命中
    const user3 = await getCachedUserData(456);
    console.log('不同用户请求结果:', user3);
    
    // 查看缓存统计
    console.log('缓存统计:', cache.getStats());
}
}

// 7. 流式处理大量数据 - 分批处理避免内存溢出
async function* processLargeDataset(data) {
    for (let i = 0; i < data.length; i += 100) {
        const batch = data.slice(i, i + 100);
        const processed = await processBatch(batch);
        yield processed;
    }
}

async function handleLargeDataset(data) {
    for await (const batch of processLargeDataset(data)) {
        console.log('处理了一批数据:', batch.length);
        // 可以在这里更新进度条
    }
}

// 8. 优雅的错误处理 - 部分失败也能继续
async function robustDataFetching() {
    const operations = [
        { name: '用户数据', fn: () => fetchUserData() },
        { name: '配置数据', fn: () => fetchConfig() },
        { name: '权限数据', fn: () => fetchPermissions() }
    ];
    
    const results = await Promise.allSettled(
        operations.map(op => op.fn())
    );
    
    const successful = [];
    const failed = [];
    
    results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
            successful.push({
                name: operations[index].name,
                data: result.value
            });
        } else {
            failed.push({
                name: operations[index].name,
                error: result.reason
            });
        }
    });
    
    if (failed.length > 0) {
        console.warn('部分数据获取失败:', failed);
    }
    
    return successful;
}

// 9. 防抖的异步操作 - 避免频繁调用
function debounceAsync(fn, delay) {
    let timeoutId;
    let currentPromise;
    
    return function(...args) {
        return new Promise((resolve, reject) => {
            clearTimeout(timeoutId);
            
            timeoutId = setTimeout(async () => {
                try {
                    if (currentPromise) {
                        await currentPromise;
                    }
                    currentPromise = fn.apply(this, args);
                    const result = await currentPromise;
                    resolve(result);
                } catch (error) {
                    reject(error);
                } finally {
                    currentPromise = null;
                }
            }, delay);
        });
    };
}

const debouncedSearch = debounceAsync(async (query) => {
    const response = await fetch(`/api/search?q=${query}`);
    return response.json();
}, 300);

// 10. 取消异步操作 - 用户可以中断请求
class CancellableOperation {
    constructor() {
        this.abortController = new AbortController();
    }
    
    async execute(url) {
        try {
            const response = await fetch(url, {
                signal: this.abortController.signal
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('操作被取消');
                return null;
            }
            throw error;
        }
    }
    
    cancel() {
        this.abortController.abort();
    }
}

// 使用取消功能
const operation = new CancellableOperation();
const dataPromise = operation.execute('/api/data');

// 5秒后取消操作
setTimeout(() => {
    operation.cancel();
}, 5000);

// 11. 监控异步操作性能 - 记录执行时间
async function monitoredFetch(url, operationName) {
    const startTime = performance.now();
    
    try {
        console.log(`开始执行: ${operationName}`);
        const result = await fetch(url);
        const endTime = performance.now();
        
        console.log(`${operationName} 完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
        return result;
    } catch (error) {
        const endTime = performance.now();
        console.error(`${operationName} 失败，耗时: ${(endTime - startTime).toFixed(2)}ms`, error);
        throw error;
    }
}

// 12. 批量处理异步任务 - 分批执行避免过载
async function batchProcess(items, batchSize = 5, processor) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        console.log(`处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)}`);
        
        const batchResults = await Promise.all(
            batch.map(item => processor(item))
        );
        
        results.push(...batchResults);
        
        // 批次间稍作停顿，避免过载
        if (i + batchSize < items.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    return results;
}

// 使用示例
const userIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
// const userProfiles = await batchProcess(
//     userIds, 
//     3, 
//     async (userId) => {
//         const response = await fetch(`/api/users/${userId}`);
//         return response.json();
//     }
// );
```

#### Await常见陷阱和解决方案

```javascript
// 陷阱1：Promise.all中的错误处理不当
// 问题：Promise.all是"快速失败"的，任何一个Promise失败都会导致整体失败
// 而且无法知道具体是哪个操作失败，也无法获取成功的部分结果
async function badExample() {
    try {
        console.log('开始获取所有数据...');
        // 如果fetchData2()失败，即使fetchData1()和fetchData3()成功，
        // 也无法获取到它们的结果
        const [data1, data2, data3] = await Promise.all([
            fetchData1(),  // 假设这个成功
            fetchData2(),  // 假设这个失败
            fetchData3()   // 假设这个成功
        ]);
        return { data1, data2, data3 };
    } catch (error) {
        // 只能知道"某个操作失败了"，但不知道是哪个，也拿不到成功的数据
        console.error('某个操作失败了:', error);
        return null; // 所有数据都丢失了
    }
}

// 解决方案：使用Promise.allSettled获取所有结果
async function goodExample() {
    console.log('开始获取所有数据（容错版本）...');
    
    // Promise.allSettled会等待所有Promise完成，无论成功失败
    const results = await Promise.allSettled([
        fetchData1(),
        fetchData2(),
        fetchData3()
    ]);
    
    const data = {};
    const errors = [];
    
    // 分别处理每个结果
    results.forEach((result, index) => {
        const dataKey = `data${index + 1}`;
        
        if (result.status === 'fulfilled') {
            // 成功的操作，保存结果
            data[dataKey] = result.value;
            console.log(`${dataKey} 获取成功`);
        } else {
            // 失败的操作，记录错误但不影响其他数据
            errors.push({ 
                operation: dataKey, 
                error: result.reason.message 
            });
            console.error(`${dataKey} 获取失败:`, result.reason.message);
        }
    });
    
    // 返回成功的数据和错误信息
    return { 
        data, 
        errors,
        hasErrors: errors.length > 0,
        successCount: Object.keys(data).length
    };
}

// 陷阱2：在forEach中使用await导致的异步问题
// 问题：forEach不会等待异步操作完成，导致时序混乱
async function badForEach(items) {
    console.log('开始处理项目...');
    
    // ❌ 错误：forEach不会等待async回调函数
    items.forEach(async (item, index) => {
        console.log(`开始处理项目 ${index + 1}: ${item}`);
        const result = await processItem(item); // 这些操作是并行的，但无法控制
        console.log(`项目 ${index + 1} 处理完成: ${result}`);
    });
    
    // 这行会立即执行，不会等待上面的处理完成
    console.log('所有项目处理完成'); // 实际上还没完成！
}

// 解决方案：使用正确的循环方式
async function goodForEach(items) {
    console.log(`开始处理${items.length}个项目...`);
    
    // 方案1：串行处理 - 一个接一个处理，保证顺序
    console.log('=== 串行处理 ===');
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        console.log(`开始处理项目 ${i + 1}: ${item}`);
        const result = await processItem(item); // 等待当前项目完成再处理下一个
        console.log(`项目 ${i + 1} 处理完成: ${result}`);
    }
    console.log('串行处理完成');
    
    // 方案2：并行处理 - 同时处理所有项目，速度更快
    console.log('=== 并行处理 ===');
    const promises = items.map((item, index) => {
        console.log(`启动项目 ${index + 1}: ${item}`);
        return processItem(item);
    });
    
    const results = await Promise.all(promises); // 等待所有项目完成
    
    results.forEach((result, index) => {
        console.log(`项目 ${index + 1} 处理完成: ${result}`);
    });
    
    console.log('并行处理完成'); // 现在会在所有处理完成后执行
    return results;
}

// 陷阱3：不必要的串行化导致性能问题
// 问题：独立的异步操作被串行执行，浪费时间
async function inefficientCode() {
    console.log('开始获取数据（低效版本）...');
    const startTime = Date.now();
    
    // ❌ 错误：这些操作是独立的，可以并行执行
    // 但这里串行执行，总时间 = 三个操作时间之和
    const user = await fetchUser();           // 等待1秒
    console.log('用户数据获取完成');
    
    const settings = await fetchSettings();   // 再等待1秒
    console.log('设置数据获取完成');
    
    const notifications = await fetchNotifications(); // 再等待1秒
    console.log('通知数据获取完成');
    
    const totalTime = Date.now() - startTime;
    console.log(`串行执行总耗时: ${totalTime}ms`); // 约3秒
    
    return { user, settings, notifications };
}

// 解决方案：并行执行独立操作
async function efficientCode() {
    console.log('开始获取数据（高效版本）...');
    const startTime = Date.now();
    
    // ✅ 正确：并行执行独立操作，总时间 ≈ 最慢操作的时间
    const [user, settings, notifications] = await Promise.all([
        fetchUser(),           // 同时开始
        fetchSettings(),       // 同时开始  
        fetchNotifications()   // 同时开始
    ]);
    
    const totalTime = Date.now() - startTime;
    console.log(`并行执行总耗时: ${totalTime}ms`); // 约1秒
    
    console.log('所有数据获取完成');
    return { user, settings, notifications };
}

// 模拟异步操作
async function fetchUser() {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id: 1, name: '用户' };
}

async function fetchSettings() {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { theme: 'dark', language: 'zh' };
}

async function fetchNotifications() {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return [{ id: 1, message: '新消息' }];
}

async function processItem(item) {
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 500));
    return `${item}_processed`;
}

// 陷阱4：忽略错误传播
async function badErrorHandling() {
    try {
        const data = await fetchData();
        return processData(data); // 如果processData抛出错误，不会被捕获
    } catch (error) {
        console.error('获取数据失败:', error);
        return null;
    }
}

// 解决方案：正确的错误处理
async function goodErrorHandling() {
    try {
        const data = await fetchData();
        return await processData(data); // 添加await确保错误被捕获
    } catch (error) {
        if (error.name === 'NetworkError') {
            console.error('网络错误:', error);
            return getDefaultData();
        } else if (error.name === 'ProcessingError') {
            console.error('数据处理错误:', error);
            return null;
        } else {
            console.error('未知错误:', error);
            throw error; // 重新抛出未知错误
        }
    }
}

// 陷阱5：await在条件语句中的误用
async function badConditionalAwait(condition) {
    // ❌ 即使条件为false，Promise也会被创建
    const result = condition ? await expensiveOperation() : null;
    return result;
}

// 解决方案：正确的条件await
async function goodConditionalAwait(condition) {
    // ✅ 只在需要时创建和等待Promise
    if (condition) {
        return await expensiveOperation();
    }
    return null;
}

// 陷阱6：忘记处理Promise rejection
async function badPromiseHandling() {
    // ❌ 如果Promise被拒绝，会导致未处理的Promise rejection
    const promises = [
        fetchData1(),
        fetchData2(),
        fetchData3()
    ];
    
    // 某些Promise可能失败，但没有处理
    return promises;
}

// 解决方案：正确处理所有Promise
async function goodPromiseHandling() {
    const promises = [
        fetchData1().catch(err => ({ error: err, data: null })),
        fetchData2().catch(err => ({ error: err, data: null })),
        fetchData3().catch(err => ({ error: err, data: null }))
    ];
    
    const results = await Promise.all(promises);
    return results;
}
```

#### 实际项目中的Await应用场景

```javascript
// 场景1：用户登录流程
async function userLogin(credentials) {
    try {
        // 1. 验证用户凭据
        const authResult = await authenticateUser(credentials);
        
        // 2. 并行获取用户信息和权限
        const [userProfile, permissions] = await Promise.all([
            fetchUserProfile(authResult.userId),
            fetchUserPermissions(authResult.userId)
        ]);
        
        // 3. 设置用户会话
        await setupUserSession(authResult.token);
        
        // 4. 记录登录日志
        logUserLogin(authResult.userId); // 不需要等待
        
        return {
            user: userProfile,
            permissions,
            token: authResult.token
        };
    } catch (error) {
        console.error('登录失败:', error);
        throw new Error('登录失败，请检查用户名和密码');
    }
}

// 场景2：文件上传进度跟踪
async function uploadFileWithProgress(file, onProgress) {
    try {
        // 1. 获取上传URL
        const uploadUrl = await getUploadUrl(file.name, file.size);
        
        // 2. 分块上传
        const chunkSize = 1024 * 1024; // 1MB chunks
        const totalChunks = Math.ceil(file.size / chunkSize);
        
        for (let i = 0; i < totalChunks; i++) {
            const start = i * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const chunk = file.slice(start, end);
            
            await uploadChunk(uploadUrl, chunk, i);
            
            // 更新进度
            const progress = ((i + 1) / totalChunks) * 100;
            onProgress(progress);
        }
        
        // 3. 完成上传
        const result = await completeUpload(uploadUrl);
        return result;
        
    } catch (error) {
        console.error('上传失败:', error);
        throw error;
    }
}

// 场景3：数据同步和缓存
class DataSyncManager {
    constructor() {
        this.cache = new Map();
        this.syncInProgress = new Set();
    }
    
    async syncData(dataType, forceRefresh = false) {
        // 避免重复同步
        if (this.syncInProgress.has(dataType)) {
            return this.waitForSync(dataType);
        }
        
        // 检查缓存
        if (!forceRefresh && this.cache.has(dataType)) {
            const cached = this.cache.get(dataType);
            if (Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5分钟缓存
                return cached.data;
            }
        }
        
        this.syncInProgress.add(dataType);
        
        try {
            const data = await this.fetchData(dataType);
            this.cache.set(dataType, {
                data,
                timestamp: Date.now()
            });
            
            return data;
        } finally {
            this.syncInProgress.delete(dataType);
        }
    }
    
    async waitForSync(dataType) {
        while (this.syncInProgress.has(dataType)) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return this.cache.get(dataType)?.data;
    }
    
    async fetchData(dataType) {
        // 模拟网络请求
        await new Promise(resolve => setTimeout(resolve, 1000));
        return `${dataType} data`;
    }
}
```

#### 总结
- **Promise**：像写"承诺书"，用`.then()`和`.catch()`处理结果
- **Async/Await**：让异步代码看起来像同步代码，更容易理解
- **并行执行**：用`Promise.all()`同时做多件事，节省时间
- **串行执行**：用多个`await`按顺序做事，前一个完成再做下一个
- **错误处理**：用`try-catch`捕获错误，就像处理普通代码一样
- **性能优化**：避免不必要的串行化，合理使用并行处理
- **错误恢复**：使用`Promise.allSettled()`处理部分失败的情况
- **资源管理**：实现超时、重试、取消等机制提高健壮性
- **实际应用**：在真实项目中灵活运用各种await模式解决复杂问题

### 7.4 事件循环和微任务
```javascript
// 理解事件循环
console.log('1');

setTimeout(() => console.log('2'), 0);

Promise.resolve().then(() => console.log('3'));

console.log('4');

// 输出顺序：1, 4, 3, 2
// 解释：
// 1. 同步代码先执行：1, 4
// 2. 微任务（Promise）优先于宏任务（setTimeout）：3
// 3. 最后执行宏任务：2

// 微任务和宏任务的区别
console.log('=== 事件循环示例 ===');

setTimeout(() => console.log('setTimeout 1'), 0);

Promise.resolve().then(() => {
    console.log('Promise 1');
    return Promise.resolve();
}).then(() => {
    console.log('Promise 2');
});

setTimeout(() => console.log('setTimeout 2'), 0);

Promise.resolve().then(() => console.log('Promise 3'));

console.log('同步代码');

// 输出：
// 同步代码
// Promise 1
// Promise 3
// Promise 2
// setTimeout 1
// setTimeout 2

// queueMicrotask API
queueMicrotask(() => {
    console.log('微任务：queueMicrotask');
});

Promise.resolve().then(() => {
    console.log('微任务：Promise.then');
});

console.log('同步代码');

// 输出：
// 同步代码
// 微任务：queueMicrotask
// 微任务：Promise.then
```

## 8. ES6+新特性

### 8.1 模板字符串
```javascript
let name = "小王";
let age = 25;

// 基本用法
let message = `你好，我是${name}，今年${age}岁`;
console.log(message);

// 多行字符串
let multilineString = `
这是第一行
这是第二行
这是第三行
`;

// 表达式插值
let a = 10, b = 20;
let result = `${a} + ${b} = ${a + b}`;
console.log(result); // "10 + 20 = 30"

// 函数调用
function formatCurrency(amount) {
    return `¥${amount.toFixed(2)}`;
}

let price = 99.5;
let priceText = `商品价格：${formatCurrency(price)}`;
console.log(priceText); // "商品价格：¥99.50"

// 嵌套模板字符串
let users = [
    { name: '张三', role: 'admin' },
    { name: '李四', role: 'user' }
];

let userList = `
<ul>
    ${users.map(user => `
        <li class="${user.role}">
            ${user.name} (${user.role})
        </li>
    `).join('')}
</ul>
`;

console.log(userList);
```

#### 8.1.1 标签模板
```javascript
// 标签模板函数
function highlight(strings, ...values) {
    return strings.reduce((result, string, i) => {
        const value = values[i] ? `<strong>${values[i]}</strong>` : '';
        return result + string + value;
    }, '');
}

let name = "JavaScript";
let level = "高级";
let html = highlight`我正在学习${name}的${level}知识`;
console.log(html); // "我正在学习<strong>JavaScript</strong>的<strong>高级</strong>知识"

// 国际化标签模板
function i18n(strings, ...values) {
    // 这里可以实现国际化逻辑
    const translations = {
        'Hello, my name is': '你好，我的名字是',
        'and I am': '我今年',
        'years old': '岁'
    };
    
    return strings.reduce((result, string, i) => {
        const translatedString = translations[string.trim()] || string;
        const value = values[i] || '';
        return result + translatedString + value;
    }, '');
}

// 使用（注意：实际使用时需要更复杂的实现）
// let greeting = i18n`Hello, my name is ${name} and I am ${age} years old`;

// SQL查询标签模板（防止SQL注入）
function sql(strings, ...values) {
    let query = '';
    for (let i = 0; i < strings.length; i++) {
        query += strings[i];
        if (i < values.length) {
            // 这里应该进行SQL转义
            query += escapeSQL(values[i]);
        }
    }
    return query;
}

function escapeSQL(value) {
    // 简单的转义示例（实际应用需要更完善的转义）
    return String(value).replace(/'/g, "''");
}

let userId = 123;
let userName = "O'Connor";
let query = sql`SELECT * FROM users WHERE id = ${userId} AND name = '${userName}'`;
console.log(query);

// 样式化输出标签模板
function styled(strings, ...values) {
    let result = '';
    for (let i = 0; i < strings.length; i++) {
        result += strings[i];
        if (i < values.length) {
            result += `%c${values[i]}%c`;
        }
    }
    
    // 返回样式化的控制台输出
    const styles = values.map(() => [
        'color: red; font-weight: bold',
        'color: black; font-weight: normal'
    ]).flat();
    
    console.log(result, ...styles);
}

styled`用户 ${name} 的年龄是 ${age}`;
```

### 8.2 解构赋值

#### 8.2.1 数组解构
```javascript
// 基本数组解构
let [a, b, c] = [1, 2, 3];
console.log(a, b, c); // 1, 2, 3

// 跳过某些元素
let [first, , third] = [1, 2, 3];
console.log(first, third); // 1, 3

// 默认值
let [x = 10, y = 20] = [5];
console.log(x, y); // 5, 20

// 剩余元素
let [head, ...tail] = [1, 2, 3, 4, 5];
console.log(head); // 1
console.log(tail); // [2, 3, 4, 5]

// 交换变量
let m = 10, n = 20;
[m, n] = [n, m];
console.log(m, n); // 20, 10

// 函数返回多个值
function getCoordinates() {
    return [100, 200];
}

let [x_coord, y_coord] = getCoordinates();
console.log(x_coord, y_coord); // 100, 200

// 嵌套数组解构
let [a1, [b1, c1]] = [1, [2, 3]];
console.log(a1, b1, c1); // 1, 2, 3

// 字符串解构
let [char1, char2, char3] = "ABC";
console.log(char1, char2, char3); // "A", "B", "C"

// 可迭代对象解构
function* generator() {
    yield 1;
    yield 2;
    yield 3;
}

let [g1, g2, g3] = generator();
console.log(g1, g2, g3); // 1, 2, 3
```

#### 8.2.2 对象解构
```javascript
// 基本对象解构
let person = { name: "张三", age: 25, city: "北京" };
let { name, age, city } = person;
console.log(name, age, city); // "张三", 25, "北京"

// 重命名变量
let { name: userName, age: userAge } = person;
console.log(userName, userAge); // "张三", 25

// 默认值
let { name: n, age: a, country = "中国" } = person;
console.log(n, a, country); // "张三", 25, "中国"

// 嵌套对象解构
let user = {
    id: 1,
    name: "李四",
    address: {
        street: "长安街",
        city: "北京",
        country: "中国"
    },
    hobbies: ["读书", "游泳"]
};

let {
    name: username,
    address: { street, city: userCity },
    hobbies: [hobby1, hobby2]
} = user;

console.log(username, street, userCity, hobby1, hobby2);

// 剩余属性
let { name: personName, ...restProps } = person;
console.log(personName); // "张三"
console.log(restProps); // { age: 25, city: "北京" }

// 动态属性名解构
let key = "name";
let { [key]: dynamicValue } = person;
console.log(dynamicValue); // "张三"

// 函数参数解构
function greetUser({ name, age, city = "未知" }) {
    return `你好，我是${name}，今年${age}岁，来自${city}`;
}

console.log(greetUser(person));

// 混合解构
let data = {
    users: [
        { id: 1, name: "用户1" },
        { id: 2, name: "用户2" }
    ],
    meta: {
        total: 2,
        page: 1
    }
};

let {
    users: [firstUser, secondUser],
    meta: { total }
} = data;

console.log(firstUser.name, secondUser.name, total);

// 解构赋值在循环中的应用
let users = [
    { name: "Alice", age: 25 },
    { name: "Bob", age: 30 },
    { name: "Charlie", age: 35 }
];

for (let { name, age } of users) {
    console.log(`${name} is ${age} years old`);
}

// Map对象的解构
let map = new Map([
    ['name', '王五'],
    ['age', 28],
    ['city', '上海']
]);

for (let [key, value] of map) {
    console.log(`${key}: ${value}`);
}
```

### 8.3 展开运算符（Spread Operator）

#### 8.3.1 数组展开
```javascript
// 基本数组展开
let arr1 = [1, 2, 3];
let arr2 = [4, 5, 6];
let combined = [...arr1, ...arr2];
console.log(combined); // [1, 2, 3, 4, 5, 6]

// 在数组中间插入元素
let middle = [10, 20];
let result = [...arr1, ...middle, ...arr2];
console.log(result); // [1, 2, 3, 10, 20, 4, 5, 6]

// 复制数组（浅拷贝）
let original = [1, 2, 3];
let copy = [...original];
copy.push(4);
console.log(original); // [1, 2, 3] - 原数组不变
console.log(copy); // [1, 2, 3, 4]

// 将类数组对象转换为数组
function convertArguments() {
    let args = [...arguments];
    console.log(Array.isArray(args)); // true
    return args;
}

convertArguments(1, 2, 3, 4);

// NodeList转数组
// let divs = [...document.querySelectorAll('div')];

// 字符串转字符数组
let str = "Hello";
let chars = [...str];
console.log(chars); // ['H', 'e', 'l', 'l', 'o']

// Set转数组
let set = new Set([1, 2, 3, 2, 1]);
let uniqueArray = [...set];
console.log(uniqueArray); // [1, 2, 3]

// 找数组最大值
let numbers = [3, 7, 1, 9, 2];
let max = Math.max(...numbers);
console.log(max); // 9

// 数组去重
function uniqueArray2(arr) {
    return [...new Set(arr)];
}

console.log(uniqueArray2([1, 2, 2, 3, 3, 4])); // [1, 2, 3, 4]
```

#### 8.3.2 对象展开
```javascript
// 基本对象展开
let obj1 = { a: 1, b: 2 };
let obj2 = { c: 3, d: 4 };
let merged = { ...obj1, ...obj2 };
console.log(merged); // { a: 1, b: 2, c: 3, d: 4 }

// 覆盖属性
let base = { name: "张三", age: 25 };
let updated = { ...base, age: 26, city: "北京" };
console.log(updated); // { name: "张三", age: 26, city: "北京" }

// 条件性展开
let includeExtra = true;
let config = {
    name: "配置",
    version: "1.0",
    ...(includeExtra && { debug: true, verbose: true })
};
console.log(config);

// 浅拷贝对象
let original2 = { 
    name: "原始", 
    nested: { value: 1 } 
};
let copy2 = { ...original2 };
copy2.name = "复制";
copy2.nested.value = 2;

console.log(original2.name); // "原始" - 顶层属性不受影响
console.log(original2.nested.value); // 2 - 嵌套对象被修改了

// 深拷贝需要递归处理或使用其他方法
function deepClone(obj) {
    if (obj === null || typeof obj !== "object") {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj);
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === "object") {
        const cloned = {};
        Object.keys(obj).forEach(key => {
            cloned[key] = deepClone(obj[key]);
        });
        return cloned;
    }
}

let deepCopy = deepClone(original2);
deepCopy.nested.value = 3;
console.log(original2.nested.value); // 2 - 不受影响

// 函数默认参数与展开运算符
function sum(a, b, c) {
    return a + b + c;
}
let numbers = [1, 2, 3];
console.log(sum(...numbers)); // 6
```

#### 8.3.3 函数参数展开
```javascript
// 函数调用时展开数组
function sum(a, b, c) {
    return a + b + c;
}

let numbers2 = [1, 2, 3];
console.log(sum(...numbers2)); // 6

// 与剩余参数结合
function multiply(multiplier, ...numbers) {
    return numbers.map(num => num * multiplier);
}

console.log(multiply(2, 1, 2, 3, 4)); // [2, 4, 6, 8]

// 展开多个数组作为参数
function processData(first, second, ...rest) {
    console.log('第一个:', first);
    console.log('第二个:', second);
    console.log('其余的:', rest);
}

let data1 = [1, 2];
let data2 = [3, 4, 5];
processData(...data1, ...data2); // 展开后：processData(1, 2, 3, 4, 5)

// 动态函数调用
let mathOperations = {
    add: (a, b) => a + b,
    multiply: (a, b) => a * b,
    max: Math.max,
    min: Math.min
};

let operation = 'max';
let values = [10, 5, 8, 3];
let result2 = mathOperations[operation](...values);
console.log(result2); // 10
```

### 8.4 类（Classes）

#### 8.4.1 基本类语法
```javascript
// ES6类语法
class Person {
    // 构造函数
    constructor(name, age) {
        this.name = name;
        this.age = age;
        this._id = Math.random().toString(36); // 私有属性约定（用_开头）
    }
    
    // 实例方法
    introduce() {
        return `我是${this.name}，今年${this.age}岁`;
    }
    
    // getter方法
    get description() {
        return `${this.name}（${this.age}岁）`;
    }
    
    // setter方法
    set newAge(age) {
        if (age > 0 && age < 150) {
            this.age = age;
        } else {
            throw new Error('年龄必须在0-150之间');
        }
    }
    
    // 静态方法
    static createAnonymous() {
        return new Person("匿名用户", 0);
    }
    
    // 静态属性
    static species = "Homo sapiens";
    
    // 私有方法（ES2022+）
    #validateAge(age) {
        return age > 0 && age < 150;
    }
    
    // 公共方法调用私有方法
    updateAge(newAge) {
        if (this.#validateAge(newAge)) {
            this.age = newAge;
        } else {
            throw new Error('无效年龄');
        }
    }
}

// 使用类
let person1 = new Person("张三", 25);
console.log(person1.introduce());
console.log(person1.description);

person1.newAge = 26;
console.log(person1.age); // 26

let anonymous = Person.createAnonymous();
console.log(Person.species);
```

#### 8.4.2 类继承
```javascript
// 基础类
class Animal {
    constructor(name, type) {
        this.name = name;
        this.type = type;
    }
    
    makeSound() {
        return `${this.name}发出了声音`;
    }
    
    move() {
        return `${this.name}在移动`;
    }
    
    toString() {
        return `${this.type}: ${this.name}`;
    }
}

// 继承类
class Dog extends Animal {
    constructor(name, breed) {
        super(name, '狗'); // 调用父类构造函数
        this.breed = breed;
    }
    
    // 重写父类方法
    makeSound() {
        return `${this.name}汪汪叫`;
    }
    
    // 新增方法
    fetch() {
        return `${this.name}去捡球了`;
    }
    
    // 调用父类方法
    introduce() {
        return `${super.toString()}，品种是${this.breed}`;
    }
}

class Cat extends Animal {
    constructor(name, color) {
        super(name, '猫');
        this.color = color;
    }
    
    makeSound() {
        return `${this.name}喵喵叫`;
    }
    
    climb() {
        return `${this.name}爬到了树上`;
    }
}

// 使用继承
let dog = new Dog("旺财", "金毛");
let cat = new Cat("咪咪", "橘色");

console.log(dog.introduce()); // "狗: 旺财，品种是金毛"
console.log(dog.makeSound()); // "旺财汪汪叫"
console.log(dog.fetch()); // "旺财去捡球了"

console.log(cat.makeSound()); // "咪咪喵喵叫"
console.log(cat.climb()); // "咪咪爬到了树上"
```

#### 8.4.3 高级类特性
```javascript
// 抽象基类模式
class Shape {
    constructor(name) {
        if (new.target === Shape) {
            throw new Error('不能直接实例化抽象类Shape');
        }
        this.name = name;
    }
    
    // 抽象方法（子类必须实现）
    area() {
        throw new Error('子类必须实现area方法');
    }
    
    // 具体方法
    toString() {
        return `${this.name}: 面积 = ${this.area()}`;
    }
}

class Rectangle extends Shape {
    constructor(width, height) {
        super('矩形');
        this.width = width;
        this.height = height;
    }
    
    area() {
        return this.width * this.height;
    }
}

class Circle extends Shape {
    constructor(radius) {
        super('圆形');
        this.radius = radius;
    }
    
    area() {
        return Math.PI * this.radius * this.radius;
    }
}

// 使用
let rect = new Rectangle(5, 3);
let circle = new Circle(2);

console.log(rect.toString()); // "矩形: 面积 = 15"
console.log(circle.toString()); // "圆形: 面积 = 12.566370614359172"

// Mixin模式
let Flyable = {
    fly() {
        return `${this.name}在飞行`;
    }
};

let Swimmable = {
    swim() {
        return `${this.name}在游泳`;
    }
};

// 将mixin应用到类
function applyMixins(targetClass, ...mixins) {
    mixins.forEach(mixin => {
        Object.getOwnPropertyNames(mixin).forEach(name => {
            if (name !== 'constructor') {
                targetClass.prototype[name] = mixin[name];
            }
        });
    });
}

class Duck extends Animal {
    constructor(name) {
        super(name, '鸭子');
    }
    
    makeSound() {
        return `${this.name}嘎嘎叫`;
    }
}

// 应用mixins
applyMixins(Duck, Flyable, Swimmable);

let duck = new Duck("唐老鸭");
console.log(duck.makeSound()); // "唐老鸭嘎嘎叫"
console.log(duck.fly()); // "唐老鸭在飞行"
console.log(duck.swim()); // "唐老鸭在游泳"

// 类装饰器模式（概念演示）
function logged(target) {
    const originalMethods = Object.getOwnPropertyNames(target.prototype);
    
    originalMethods.forEach(methodName => {
        if (methodName !== 'constructor') {
            const originalMethod = target.prototype[methodName];
            if (typeof originalMethod === 'function') {
                target.prototype[methodName] = function(...args) {
                    console.log(`调用方法: ${methodName}`);
                    return originalMethod.apply(this, args);
                };
            }
        }
    });
    
    return target;
}

// 应用装饰器（手动）
@logged  // 注意：装饰器语法还在提案阶段
class Calculator {
    add(a, b) {
        return a + b;
    }
    
    multiply(a, b) {
        return a * b;
    }
}

// 手动应用装饰器
Calculator = logged(Calculator);

let calc = new Calculator();
console.log(calc.add(2, 3)); // 会先输出 "调用方法: add"，然后输出 5
```

### 8.5 Symbol

#### 8.5.1 Symbol基础
```javascript
// 创建Symbol
let sym1 = Symbol();
let sym2 = Symbol('description');
let sym3 = Symbol('description');

console.log(sym2 === sym3); // false - 每个Symbol都是唯一的
console.log(sym2.toString()); // "Symbol(description)"
console.log(sym2.description); // "description"

// Symbol作为对象属性
let obj = {};
let symKey = Symbol('myKey');

obj[symKey] = 'Symbol属性值';
console.log(obj[symKey]); // "Symbol属性值"

// Symbol属性不会出现在for...in循环中
obj.regularProp = '普通属性';

for (let key in obj) {
    console.log(key); // 只输出 "regularProp"
}

// 获取Symbol属性需要特殊方法
console.log(Object.getOwnPropertySymbols(obj)); // [Symbol(myKey)]
console.log(Reflect.ownKeys(obj)); // ["regularProp", Symbol(myKey)]
```

#### 8.5.2 全局Symbol注册表
```javascript
// 全局Symbol注册表
let globalSym1 = Symbol.for('app.config');
let globalSym2 = Symbol.for('app.config');

console.log(globalSym1 === globalSym2); // true - 全局注册表中的Symbol是共享的

// 获取Symbol的key
console.log(Symbol.keyFor(globalSym1)); // "app.config"
console.log(Symbol.keyFor(Symbol('local'))); // undefined - 本地Symbol没有key

// 实际应用：避免属性名冲突
const CONFIG_SYMBOL = Symbol.for('app.config');

class AppModule {
    constructor() {
        this[CONFIG_SYMBOL] = {
            apiUrl: 'https://api.example.com',
            timeout: 5000
        };
    }
    
    getConfig() {
        return this[CONFIG_SYMBOL];
    }
}

let module1 = new AppModule();
let module2 = new AppModule();

// 两个模块可以安全地使用相同的Symbol
console.log(module1.getConfig());
console.log(module2.getConfig());
```

#### 8.5.3 内置Symbol
```javascript
// Symbol.iterator - 定义对象的默认迭代器
class NumberRange {
    constructor(start, end) {
        this.start = start;
        this.end = end;
    }
    
    // 实现迭代器接口
    [Symbol.iterator]() {
        let current = this.start;
        const end = this.end;
        
        return {
            next() {
                if (current <= end) {
                    return { value: current++, done: false };
                } else {
                    return { done: true };
                }
            }
        };
    }
}

let range = new NumberRange(1, 5);
for (let num of range) {
    console.log(num); // 1, 2, 3, 4, 5
}

// Symbol.toStringTag - 自定义对象的字符串表示
class MyClass {
    get [Symbol.toStringTag]() {
        return 'MyCustomClass';
    }
}

let obj2 = new MyClass();
console.log(obj2.toString()); // "[object MyCustomClass]"

// Symbol.hasInstance - 自定义instanceof行为
class MyArray {
    static [Symbol.hasInstance](instance) {
        return Array.isArray(instance);
    }
}

console.log([] instanceof MyArray); // true
console.log({} instanceof MyArray); // false

// Symbol.toPrimitive - 自定义类型转换
class Temperature {
    constructor(celsius) {
        this.celsius = celsius;
    }
    
    [Symbol.toPrimitive](hint) {
        switch (hint) {
            case 'number':
                return this.celsius;
            case 'string':
                return `${this.celsius}°C`;
            default:
                return `Temperature: ${this.celsius}°C`;
        }
    }
}

let temp = new Temperature(25);
console.log(+temp); // 25 (number context)
console.log(`${temp}`); // "25°C" (string context)
console.log(temp + ""); // "Temperature: 25°C" (default context)
```

### 8.6 其他ES6+特性

#### 8.6.1 Set和Map
```javascript
// Set - 唯一值的集合
let mySet = new Set();

mySet.add(1);
mySet.add(2);
mySet.add(2); // 重复值不会被添加
mySet.add('hello');

console.log(mySet.size); // 3
console.log(mySet.has(1)); // true
console.log(mySet.has(3)); // false

// 遍历Set
for (let value of mySet) {
    console.log(value);
}

// Set与数组的转换
let arr = [1, 2, 3, 2, 1];
let uniqueSet = new Set(arr);
let uniqueArr = [...uniqueSet];
console.log(uniqueArr); // [1, 2, 3]

// Set操作
let setA = new Set([1, 2, 3]);
let setB = new Set([3, 4, 5]);

// 并集
let union = new Set([...setA, ...setB]);
console.log(union); // Set {1, 2, 3, 4, 5}

// 交集
let intersection = new Set([...setA].filter(x => setB.has(x)));
console.log(intersection); // Set {3}

// 差集
let difference = new Set([...setA].filter(x => !setB.has(x)));
console.log(difference); // Set {1, 2}

// WeakSet - 弱引用Set
let weakSet = new WeakSet();
let obj1 = {};
let obj2 = {};

weakSet.add(obj1);
weakSet.add(obj2);

console.log(weakSet.has(obj1)); // true

// Map - 键值对集合
let myMap = new Map();

myMap.set('name', '张三');
myMap.set('age', 25);
myMap.set(1, 'number key');
myMap.set(true, 'boolean key');

console.log(myMap.get('name')); // "张三"
console.log(myMap.size); // 4

// 对象作为键
let keyObj = {};
myMap.set(keyObj, 'object key value');
console.log(myMap.get(keyObj)); // "object key value"

// 遍历Map
for (let [key, value] of myMap) {
    console.log(`${key}: ${value}`);
}

// Map与对象的区别和转换
let mapFromObject = new Map(Object.entries({a: 1, b: 2}));
let objectFromMap = Object.fromEntries(myMap);

console.log(mapFromObject);
console.log(objectFromMap);

// WeakMap - 弱引用Map
let weakMap = new WeakMap();
let key1 = {};
let key2 = {};

weakMap.set(key1, 'value1');
weakMap.set(key2, 'value2');

console.log(weakMap.get(key1)); // "value1"
```

#### 8.6.2 Proxy和Reflect
```javascript
// Proxy - 对象代理
let target = {
    name: '张三',
    age: 25
};

let proxy = new Proxy(target, {
    // 拦截属性读取
    get(target, property, receiver) {
        console.log(`读取属性: ${property}`);
        return Reflect.get(target, property, receiver);
    },
    
    // 拦截属性设置
    set(target, property, value, receiver) {
        console.log(`设置属性: ${property} = ${value}`);
        
        // 验证年龄
        if (property === 'age' && (value < 0 || value > 150)) {
            throw new Error("除数不能为零");
        }
        
        return Reflect.set(target, property, value, receiver);
    },
    
    // 拦截属性枚举
    ownKeys(target) {
        console.log('枚举属性');
        return Reflect.ownKeys(target);
    },
    
    // 拦截属性删除
    deleteProperty(target, property) {
        console.log(`删除属性: ${property}`);
        return Reflect.deleteProperty(target, property);
    }
});

proxy.name; // 输出: "读取属性: name"
proxy.age = 30; // 输出: "设置属性: age = 30"

// 数组代理示例
function createObservableArray(arr) {
    return new Proxy(arr, {
        set(target, property, value, receiver) {
            console.log(`数组索引 ${property} 被设置为 ${value}`);
            return Reflect.set(target, property, value, receiver);
        }
    });
}

let observableArr = createObservableArray([1, 2, 3]);
observableArr[0] = 10; // 输出: "数组索引 0 被设置为 10"
observableArr.push(4); // 会触发多次set拦截

// 函数代理
function originalFunction(a, b) {
    return a + b;
}

let functionProxy = new Proxy(originalFunction, {
    apply(target, thisArg, argumentsList) {
        console.log(`函数被调用，参数: ${argumentsList.join(', ')}`);
        let result = Reflect.apply(target, thisArg, argumentsList);
        console.log(`函数返回: ${result}`);
        return result;
    }
});

functionProxy(3, 4); // 输出函数调用信息和结果

// Reflect - 反射API
let obj3 = { x: 1, y: 2 };

// Reflect提供了与Proxy handler方法相对应的静态方法
console.log(Reflect.get(obj3, 'x')); // 1
Reflect.set(obj3, 'z', 3);
console.log(Reflect.has(obj3, 'z')); // true
console.log(Reflect.ownKeys(obj3)); // ['x', 'y', 'z']

// Reflect.defineProperty
let success = Reflect.defineProperty(obj3, 'w', {
    value: 4,
    writable: false,
    enumerable: true,
    configurable: true
});
console.log(success); // true
```

## 9. 模块化

### 9.1 模块化发展历史
```javascript
// 1. 全局变量方式（早期，不推荐）
// global.js
var GlobalModule = {
    config: {
        apiUrl: 'https://api.example.com'
    },
    
    utils: {
        formatDate: function(date) {
            return date.toLocaleDateString();
        }
    }
};

// 问题：全局污染、命名冲突、依赖管理困难

// 2. IIFE模式（立即执行函数表达式）
var MyModule = (function() {
    // 私有变量
    var privateVar = '私有数据';
    var config = {
        version: '1.0.0'
    };
    
    // 私有方法
    function privateMethod() {
        return '私有方法';
    }
    
    // 公开接口
    return {
        publicMethod: function() {
            return privateMethod() + ' - ' + privateVar;
        },
        
        getConfig: function() {
            return Object.assign({}, config); // 返回副本
        },
        
        setConfig: function(newConfig) {
            Object.assign(config, newConfig);
        }
    };
})();

// 使用模块
console.log(MyModule.publicMethod());
console.log(MyModule.getConfig());

// 3. 模块模式增强版（支持依赖注入）
var EnhancedModule = (function(dependency1, dependency2) {
    var privateData = 'enhanced data';
    
    return {
        method1: function() {
            return dependency1.someMethod() + privateData;
        },
        
        method2: function() {
            return dependency2.anotherMethod();
        }
    };
})(SomeDependency, AnotherDependency);
```

### 9.2 CommonJS（Node.js）

#### 9.2.1 基本用法
```javascript
// math.js - 导出模块
function add(a, b) {
    return a + b;
}

function subtract(a, b) {
    return a - b;
}

function multiply(a, b) {
    return a * b;
}

// 方式1：逐个导出
exports.add = add;
exports.subtract = subtract;

// 方式2：整体导出
module.exports = {
    add,
    subtract,
    multiply
};

// 方式3：混合导出
module.exports = multiply; // 默认导出
module.exports.add = add;  // 命名导出
module.exports.subtract = subtract;

// calculator.js - 导入模块
const math = require('./math');
const { add, subtract } = require('./math'); // 解构导入
const multiply = require('./math'); // 如果是默认导出

console.log(math.add(5, 3)); // 8
console.log(add(5, 3)); // 8
console.log(multiply(4, 3)); // 12

// 导入内置模块
const fs = require('fs');
const path = require('path');
const http = require('http');

// 导入npm包
const express = require('express');
const lodash = require('lodash');
```

#### 9.2.2 模块缓存机制
```javascript
// counter.js
let count = 0;

function increment() {
    count++;
    return count;
}

function getCount() {
    return count;
}

module.exports = {
    increment,
    getCount
};

// app.js
const counter1 = require('./counter');
const counter2 = require('./counter'); // 同一个模块实例

console.log(counter1.increment()); // 1
console.log(counter2.increment()); // 2 - 共享状态
console.log(counter1.getCount()); // 2

// 查看模块缓存
console.log(require.cache);

// 删除模块缓存（谨慎使用）
delete require.cache[require.resolve('./counter')];
const counter3 = require('./counter'); // 新的模块实例
console.log(counter3.getCount()); // 0 - 重新开始
```

#### 9.2.3 循环依赖处理
```javascript
// a.js
console.log('a starting');
exports.done = false;
const b = require('./b.js');
console.log('in a, b.done = %j', b.done);
exports.done = true;
console.log('a done');

// b.js
console.log('b starting');
exports.done = false;
const a = require('./a.js');
console.log('in b, a.done = %j', a.done);
exports.done = true;
console.log('b done');

// main.js
console.log('main starting');
const a = require('./a.js');
const b = require('./b.js');
console.log('in main, a.done = %j, b.done = %j', a.done, b.done);

// 输出：
// main starting
// a starting
// b starting
// in b, a.done = false
// b done
// in a, b.done = true
// a done
// in main, a.done = true, b.done = true
```

### 9.3 ES6模块（ESM）

#### 9.3.1 基本语法
```javascript
// utils.js - 导出模块

// 命名导出
export function formatDate(date) {
    return date.toLocaleDateString('zh-CN');
}

export const PI = 3.14159;

export class Logger {
    constructor(name) {
        this.name = name;
    }
    
    log(message) {
        console.log(`[${this.name}] ${message}`);
    }
}

// 批量导出
function helper1() { return 'helper1'; }
function helper2() { return 'helper2'; }
const config = { version: '1.0' };

export { helper1, helper2, config };

// 重命名导出
export { helper1 as h1, helper2 as h2 };

// 默认导出
export default function calculator(a, b, operation) {
    switch(operation) {
        case '+': return a + b;
        case '-': return a - b;
        case '*': return a * b;
        case '/': return a / b;
        default: return 0;
    }
}

// 同时有默认导出和命名导出
// export default calculator;
// export { formatDate, PI, Logger };
```

#### 9.3.2 导入语法
```javascript
// app.js - 导入模块

// 默认导入
import calculator from './utils.js';

// 命名导入
import { formatDate, PI, Logger } from './utils.js';

// 重命名导入
import { formatDate as format, PI as pi } from './utils.js';

// 全部导入
import * as Utils from './utils.js';

// 混合导入
import calculator, { formatDate, PI } from './utils.js';

// 仅执行模块（不导入任何内容）
import './side-effects.js';

// 动态导入（ES2020）
async function loadModule() {
    const module = await import('./utils.js');
    console.log(module.formatDate(new Date()));
}

// 条件导入
if (someCondition) {
    import('./optional-module.js')
        .then(module => {
            module.doSomething();
        })
        .catch(error => {
            console.error('模块加载失败:', error);
        });
}

// 使用导入的模块
console.log(calculator(10, 5, '+')); // 15
console.log(formatDate(new Date()));
console.log(PI); // 3.14159

const logger = new Logger('App');
logger.log('应用启动');

// 使用全部导入
console.log(Utils.formatDate(new Date()));
console.log(Utils.PI);
```

#### 9.3.3 模块的高级特性
```javascript
// re-export（重新导出）
// index.js - 聚合模块
export { formatDate, PI } from './utils.js';
export { default as Calculator } from './calculator.js';
export * from './helpers.js'; // 导出所有命名导出
export * as Validators from './validators.js'; // 命名空间导出

// 这样其他模块可以从index.js导入所有功能
// import { formatDate, PI, Calculator } from './index.js';

// 模块作为配置
// config/index.js
const development = {
    apiUrl: 'http://localhost:3000',
    debug: true,
    logLevel: 'debug'
};

const production = {
    apiUrl: 'https://api.production.com',
    debug: false,
    logLevel: 'error'
};

const config = process.env.NODE_ENV === 'production' ? production : development;

export default config;

// 特性开关模块
// features/index.js
const features = {
    newDashboard: process.env.FEATURE_NEW_DASHBOARD === 'true',
    betaFeatures: process.env.FEATURE_BETA === 'true',
    analytics: process.env.FEATURE_ANALYTICS === 'true'
};

export function isFeatureEnabled(featureName) {
    return features[featureName] || false;
}

export default features;

// 使用特性开关
import { isFeatureEnabled } from './features';

if (isFeatureEnabled('newDashboard')) {
    // 加载新的仪表板
    const { NewDashboard } = await import('./components/NewDashboard');
    // 使用新组件
} else {
    // 使用旧的仪表板
    const { OldDashboard } = await import('./components/OldDashboard');
    // 使用旧组件
}
```

### 9.4 模块打包和构建

#### 9.4.1 Webpack模块处理
```javascript
// webpack.config.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
    // 入口文件
    entry: './src/index.js',
    
    // 输出配置
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'bundle.js',
        // 配置模块输出格式
        library: 'MyLibrary',
        libraryTarget: 'umd' // 支持多种模块系统
    },
    
    // 模式：development 或 production
    mode: 'development',
    
    // 开发服务器
    devServer: {
        static: './dist',
        port: 3000,
        hot: true, // 热重载
    },
    
    // 模块规则
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            }
        ]
    },
    
    // 插件
    plugins: [
        new HtmlWebpackPlugin({
            template: './public/index.html'
        })
    ]
};

// vite.config.js - Vite配置（更现代的构建工具）
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [react()],
    server: {
        port: 3000,
        open: true // 自动打开浏览器
    },
    build: {
        outDir: 'dist',
        minify: 'terser', // 代码压缩
        sourcemap: true   // 生成源码映射
    }
});
```

#### 9.4.2 Tree Shaking（树摇）
```javascript
// math.js - 提供多个函数
export function add(a, b) {
    return a + b;
}

export function subtract(a, b) {
    return a - b;
}

export function multiply(a, b) {
    return a * b;
}

export function divide(a, b) {
    return a / b;
}

export function complexCalculation() {
    // 复杂计算，很少使用
    console.log('执行复杂计算...');
    return Math.random() * 1000;
}

// app.js - 只使用部分函数
import { add, subtract } from './math.js';

console.log(add(5, 3));
console.log(subtract(10, 4));

// 在生产构建中，multiply、divide和complexCalculation
// 会被tree shaking移除，因为它们没有被使用

// package.json配置
{
    "name": "my-package",
    "sideEffects": false, // 表示包没有副作用，可以安全地tree shake
    // 或者指定有副作用的文件
    "sideEffects": [
        "*.css",
        "*.scss",
        "./src/polyfills.js"
    ]
}
```

#### 9.4.3 代码分割和懒加载
```javascript
// 动态导入实现代码分割
async function loadComponent() {
    // 这会创建一个新的chunk
    const { default: HeavyComponent } = await import('./HeavyComponent.js');
    return HeavyComponent;
}

// React中的懒加载示例
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./LazyComponent.js'));

function App() {
    return (
        <Suspense fallback={<div>加载中...</div>}>
            <LazyComponent />
        </Suspense>
    );
}

// 基于路由的代码分割
const routes = [
    {
        path: '/home',
        component: () => import('./pages/Home.js')
    },
    {
        path: '/about',
        component: () => import('./pages/About.js')
    },
    {
        path: '/contact',
        component: () => import('./pages/Contact.js')
    }
];

// 条件性加载
async function loadFeature(featureName) {
    switch (featureName) {
        case 'analytics':
            return import('./features/analytics.js');
        case 'chat':
            return import('./features/chat.js');
        case 'notifications':
            return import('./features/notifications.js');
        default:
            throw new Error(`未知功能: ${featureName}`);
    }
}

// 预加载
if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
        // 在浏览器空闲时预加载
        import('./preload-module.js');
    });
}
```

### 9.5 模块最佳实践

#### 9.5.1 模块设计原则
```javascript
// 1. 使用有意义的变量名
let userAge = 25;              // 好
let a = 25;                    // 不好

// 2. 使用常量替代魔法数字
const MAX_LOGIN_ATTEMPTS = 3;  // 好
if (attempts > 3) { ... }      // 不好

// 3. 函数应该职责单一
function calculateTax(income) { // 好：只计算税费
    return income * 0.2;
}

// 4. 使用严格模式
"use strict";

// 5. 适当的注释
/**
 * 计算两个数的最大公约数
 * @param {number} a 第一个数
 * @param {number} b 第二个数
 * @returns {number} 最大公约数
 */
function gcd(a, b) {
    while (b !== 0) {
        let temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}
```

### 9.6 常见错误避免

```javascript
// 1. 避免全局变量污染
// 不好
var globalVar = "全局变量";

// 好
(function() {
    let localVar = "局部变量";
})();

// 2. 正确使用比较运算符
// 使用严格相等
if (value === null) { /* ... */ }    // 好
if (value == null) { /* ... */ }     // 可能有问题

// 3. 避免回调地狱
// 不好
getData(function(a) {
    getMoreData(a, function(b) {
        getEvenMoreData(b, function(c) {
            // 嵌套太深
        });
    });
});

// 好
async function processData() {
    try {
        let a = await getData();
        let b = await getMoreData(a);
        let c = await getEvenMoreData(b);
        return c;
    } catch (error) {
        console.error("处理数据失败", error);
    }
}

// 4. 内存泄漏防范
// 清理定时器
let timer = setInterval(() => {
    console.log("定时执行");
}, 1000);

// 记得清理
clearInterval(timer);

// 移除事件监听器
function cleanup() {
    element.removeEventListener("click", clickHandler);
}
```

## 11. TypeScript入门

### 11.1 TypeScript基础

```typescript
// TypeScript是JavaScript的超集，添加了静态类型检查

// 基本类型注解
let userName: string = "张三";
let userAge: number = 25;
let isStudent: boolean = true;

// 数组类型
let numbers: number[] = [1, 2, 3, 4, 5];
let fruits: Array<string> = ["苹果", "香蕉", "橙子"];

// 对象类型
interface User {
    name: string;
    age: number;
    email?: string; // 可选属性
}

let user: User = {
    name: "李四",
    age: 30
};

// 函数类型
function greet(name: string): string {
    return `你好，${name}！`;
}

// 箭头函数类型
const add = (a: number, b: number): number => a + b;

// 泛型
function identity<T>(arg: T): T {
    return arg;
}

let result = identity<string>("Hello TypeScript");
```

### 11.2 高级类型

```typescript
// 联合类型
let id: string | number;
id = "abc123";
id = 123;

// 交叉类型
interface HasName {
    name: string;
}

interface HasAge {
    age: number;
}

type Person = HasName & HasAge;

let person: Person = {
    name: "王五",
    age: 28
};

// 类型别名
type Status = "pending" | "approved" | "rejected";

let currentStatus: Status = "pending";

// 枚举
enum Color {
    Red = "#ff0000",
    Green = "#00ff00",
    Blue = "#0000ff"
}

let favoriteColor: Color = Color.Blue;

// 类
class Animal {
    protected name: string;
    
    constructor(name: string) {
        this.name = name;
    }
    
    public move(): void {
        console.log(`${this.name}正在移动`);
    }
}

class Dog extends Animal {
    private breed: string;
    
    constructor(name: string, breed: string) {
        super(name);
        this.breed = breed;
    }
    
    public bark(): void {
        console.log(`${this.name}在汪汪叫`);
    }
}
```

### 11.3 实用工具类型

```typescript
// Partial：使所有属性可选
interface Todo {
    title: string;
    description: string;
    completed: boolean;
}

type PartialTodo = Partial<Todo>;
// 等同于：{ title?: string; description?: string; completed?: boolean; }

// Pick：选择指定属性
type TodoPreview = Pick<Todo, "title" | "completed">;
// 等同于：{ title: string; completed: boolean; }

// Omit：排除指定属性
type TodoWithoutDescription = Omit<Todo, "description">;
// 等同于：{ title: string; completed: boolean; }

// Record：创建键值对类型
type UserRoles = Record<string, string>;
// 等同于：{ [key: string]: string; }

// 条件类型
type NonNullable<T> = T extends null | undefined ? never : T;

// 映射类型
type Readonly<T> = {
    readonly [P in keyof T]: T[P];
};
```

## 12. 测试基础

### 12.1 单元测试（使用Jest）

```javascript
// 安装：npm install --save-dev jest

// math.js - 被测试的模块
function add(a, b) {
    return a + b;
}

function subtract(a, b) {
    return a - b;
}

function divide(a, b) {
    if (b === 0) {
        throw new Error("除数不能为零");
    }
    return a / b;
}

module.exports = { add, subtract, divide };

// math.test.js - 测试文件
const { add, subtract, divide } = require('./math');

describe('数学运算函数测试', () => {
    test('加法运算应该正确', () => {
        expect(add(2, 3)).toBe(5);
        expect(add(-1, 1)).toBe(0);
        expect(add(0, 0)).toBe(0);
    });
    
    test('减法运算应该正确', () => {
        expect(subtract(5, 3)).toBe(2);
        expect(subtract(0, 5)).toBe(-5);
    });
    
    test('除法运算应该正确', () => {
        expect(divide(10, 2)).toBe(5);
        expect(divide(9, 3)).toBe(3);
    });
    
    test('除零应该抛出错误', () => {
        expect(() => {
            divide(10, 0);
        }).toThrow('除数不能为零');
    });
});

// 异步测试
test('异步操作测试', async () => {
    const data = await fetchData();
    expect(data).toBe('获取到的数据');
});

// 模拟函数测试
test('模拟函数测试', () => {
    const mockFn = jest.fn();
    mockFn('参数1', '参数2');
    
    expect(mockFn).toHaveBeenCalled();
    expect(mockFn).toHaveBeenCalledWith('参数1', '参数2');
    expect(mockFn).toHaveBeenCalledTimes(1);
});
```

### 12.2 前端测试（使用Testing Library）

```javascript
// 安装：npm install --save-dev @testing-library/react @testing-library/jest-dom

// Button.jsx - React组件
import React from 'react';

function Button({ onClick, children, disabled = false }) {
    return (
        <button 
            onClick={onClick} 
            disabled={disabled}
            className="btn"
        >
            {children}
        </button>
    );
}

export default Button;

// Button.test.jsx - 组件测试
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Button from './Button';

describe('Button组件测试', () => {
    test('应该渲染按钮文本', () => {
        render(<Button>点击我</Button>);
        expect(screen.getByText('点击我')).toBeInTheDocument();
    });
    
    test('点击时应该调用onClick函数', () => {
        const handleClick = jest.fn();
        render(<Button onClick={handleClick}>点击我</Button>);
        
        fireEvent.click(screen.getByText('点击我'));
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    
    test('禁用状态下不应该响应点击', () => {
        const handleClick = jest.fn();
        render(
            <Button onClick={handleClick} disabled>
                点击我
            </Button>
        );
        
        const button = screen.getByText('点击我');
        expect(button).toBeDisabled();
        
        fireEvent.click(button);
        expect(handleClick).not.toHaveBeenCalled();
    });
});
```

## 13. JSON操作

```javascript
// JavaScript对象
let user = {
    name: "张三",
    age: 25,
    hobbies: ["读书", "游泳", "编程"],
    address: {
        city: "北京",
        district: "朝阳区"
    }
};

// 对象转JSON字符串
let jsonString = JSON.stringify(user);
console.log(jsonString);

// 格式化JSON输出
let formattedJson = JSON.stringify(user, null, 2);
console.log(formattedJson);

// JSON字符串转对象
let parsedUser = JSON.parse(jsonString);
console.log(parsedUser);

// 安全的JSON解析
function safeJsonParse(jsonString) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.error("JSON解析失败：", error.message);
        return null;
    }
}
```

## 14. 浏览器API（前端）

### 14.1 DOM操作
```javascript
// 选择元素
let element = document.getElementById("myId");
let elements = document.querySelectorAll(".myClass");
let firstDiv = document.querySelector("div");

// 创建和插入元素
let newDiv = document.createElement("div");
newDiv.textContent = "新创建的元素";
newDiv.className = "highlight";

document.body.appendChild(newDiv);

// 修改元素
element.innerHTML = "<strong>粗体文本</strong>";
element.style.color = "red";
element.setAttribute("data-id", "123");

// 事件监听
element.addEventListener("click", function(event) {
    console.log("元素被点击了！");
    event.preventDefault(); // 阻止默认行为
});

// 移除事件监听
function clickHandler() {
    console.log("处理点击事件");
}
element.addEventListener("click", clickHandler);
element.removeEventListener("click", clickHandler);
```

### 14.2 本地存储
```javascript
// localStorage（永久存储）
localStorage.setItem("username", "李四");
let username = localStorage.getItem("username");
localStorage.removeItem("username");
localStorage.clear(); // 清空所有

// sessionStorage（会话存储）
sessionStorage.setItem("tempData", "临时数据");

// 存储对象
let userProfile = {name: "王五", age: 28};
localStorage.setItem("profile", JSON.stringify(userProfile));
let storedProfile = JSON.parse(localStorage.getItem("profile"));
```

### 14.3 Fetch API
```javascript
// GET请求
async function fetchData() {
    try {
        let response = await fetch("https://api.example.com/data");
        
        if (!response.ok) {
            throw new Error(`HTTP错误！状态：${response.status}`);
        }
        
        let data = await response.json();
        console.log("获取的数据：", data);
        return data;
        
    } catch (error) {
        console.error("获取数据失败：", error);
    }
}

// POST请求
async function postData(userData) {
    try {
        let response = await fetch("https://api.example.com/users", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer token123"
            },
            body: JSON.stringify(userData)
        });
        
        let result = await response.json();
        console.log("创建用户成功：", result);
        
    } catch (error) {
        console.error("创建用户失败：", error);
    }
}
```

## 15. 调试和性能优化

### 15.1 调试技巧
```javascript
// 控制台输出
console.log("普通日志");
console.warn("警告信息");
console.error("错误信息");
console.table([{name: "张三", age: 25}, {name: "李四", age: 30}]);

// 计时
console.time("操作耗时");
// 执行一些操作
for (let i = 0; i < 1000000; i++) {
    // 某些计算
}
console.timeEnd("操作耗时");

// 断言
console.assert(5 > 3, "5应该大于3");

// 堆栈跟踪
function funcA() {
    funcB();
}
function funcB() {
    console.trace("调用堆栈");
}
funcA();
```

### 15.2 性能优化建议
```javascript
// 1. 避免全局变量污染
(function() {
    // 使用立即执行函数创建私有作用域
    let privateVar = "私有变量";
})();

// 2. 使用事件委托
document.addEventListener("click", function(event) {
    if (event.target.classList.contains("button")) {
        // 处理按钮点击
    }
});

// 3. 防抖和节流
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

function throttle(func, delay) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, delay);
        }
    };
}

// 4. 延迟加载
function lazyLoad() {
    // 只在需要时加载资源
    import('./heavyModule.js').then(module => {
        module.doSomething();
    });
}
```

## 总结

JavaScript是一门功能强大且不断发展的编程语言。通过系统学习以上知识点，您将能够：

- 🎯 **掌握核心概念**：变量、函数、对象等基础知识
- 🚀 **理解现代特性**：ES6+语法、异步编程、模块化
- 🛠️ **应用实际开发**：DOM操作、API调用、错误处理
- 📈 **提升代码质量**：最佳实践、性能优化、调试技巧

记住：**实践是最好的老师**！多写代码，多做项目，在实际应用中加深理解。

---

💡 **学习建议**：
- 每学完一个知识点就动手实践
- 阅读优秀的开源代码
- 参与技术社区讨论
- 保持对新技术的好奇心

🚀 **进阶方向**：
- **前端框架**：React、Vue、Angular
- **后端开发**：Node.js、Deno
- **移动开发**：React Native、Flutter（Dart）
- **桌面应用**：Electron、Tauri
- **可视化**：D3.js、Three.js
- **游戏开发**：Phaser、Cocos2d-js

祝您JavaScript学习愉快！🎉

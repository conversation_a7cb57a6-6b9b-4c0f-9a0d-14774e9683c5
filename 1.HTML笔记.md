# HTML5

### HTML5是新一代的HTML标准，2014年由万维网联盟完成标准指定

#### 优势
- 针对JavaScript，新增了很多可操作的接口
- 新增一些语义化标签、全局属性
- 新增了多媒体标签，可以很好替代flash
- 更加侧重语义化，对seo更友好
- 可移植性好，可以大量应用在移动设备上
#### 兼容性
- 支持：chrome、safari、opera、firefox

### 语义化标签
#### 新增布局标签
- header：整个页面或部分区域的头部
- nav：导航
- section：页面中某段文字或者文章中某段文字
  - section强调的是分段或者分块，如果想将一块内容分成几段的时候，可使用section元素
- article：文章、帖子、杂志、新闻、博客、评论
  - artical里面可以有多个section
  - article比section更强调独立性，一块内容如果比较独立，比较完整，应该使用article元素
- aside:侧边栏
- footer：整个页面或部分区域的底部
#### 新增状态标签
- meter：度量
  - 语义：定义已知范围内的标量测量。也被称为gauge，双标签
  - 属性
    - value:当前值
    - min:最小值
    - max:最大值
    - low:低值
    - high:高值
    - optimum:最佳值
- progress：进度条
  - 语义：显示某个任务完成得进度得指示器，一般用于表示进度条，双标签
  - 属性
    - value:当前值
    - max:最大值
#### 新增列表标签
- datalist:用于搜索框的关键词
- datails:用于展示问题和答案，或对转悠名词进行解释
- summary:写在details标签内部，用于指定问题或专有名词
![alt text](image-9.png)
#### 新增文本标签
- 文本注音
  - ruby:包裹需要注音的文字
  - rt:写注音，rt标签写在ruby里面
![alt text](image-8.png)
- 文本标记
  - mark:用于标记文本
![alt text](image-10.png)
#### 新增表单功能
##### 表单控件新增属性
- placeholder:默认提示文字，适用文字输入类的表单控件
- required:表示该项为必填项，适用除按钮外的其他表单控件
- autofocus:自动获取焦点，适用所有表单控件
- autocomplete:自动完成，可以设置on或off，适用文字输入类表单控件
- pattern:正则校验，适用文字输入类的表单控件
##### input新增属性
- type:新增类型，表单提交时会严重格式，输入为空则不验证格式
  - email:邮箱
  - url:网址
  - number:数字
  - range:滑块
  - date:日期
  - month:月份
  - week:周
  - time:时间
  - datetime:日期时间
  - datetime-local:本地日期时间
  - color:颜色
  - search:搜索框：表单提交时不会验证格式。
  - tel：电话：表单提交时不会验证格式，在移动端使用时，会唤起数字键盘
##### form标签新增属性
- novalidate:表单提交时不再进行表单验证，
- autocomplete:禁用表单自动完成
#### 新增多媒体标签
- video:视频
  - 属性
    - src:视频地址
    - controls:是否显示控制条
    - autoplay:是否自动播放
    - loop:是否循环播放
    - muted:是否静音
    - poster:封面
    - width:宽度
    - height:高度
- audio:音频
  - 属性
    - src:音频地址
    - controls:是否显示控制条
    - autoplay:是否自动播放
    - loop:是否循环播放
    - muted:是否静音
    - preload:预加载
      - auto:自动预加载
      - metadata:只加载元数据
      - none:不预加载
#### 新增全局属性（了解）
- contenteditable:是否可编辑
- draggable:是否可拖拽
- hidden:是否隐藏
- spellcheck:是否拼写检查
- contextmenu:右键菜单
- data-*:用于储存页面的私有定制数据
#### HTML5兼容性处理
- 添加元信息，让浏览器处于最优渲染模式
![alt text](image-11.png)
- 适用html5shiv让低版本浏览器认识H5比的语义化标签
![alt text](image-12.png)
![alt text](image-13.png)
# CSS学习笔记

### CSS：层叠样式表（cascading style sheets）

#### 1.三大特性

##### 1.1.层叠性

- 如果发生样式冲突，那就会根据一顶的规则（选择器优先级），进行样式的层叠（覆盖）
- 冲突：元素的同一个样式名，被设置了不同的值，这就冲突了

##### 1.2.继承性

- 元素会自动拥有其父元素，或者其祖先元素上所设置的某些样式
- 规则：优先继承离得近的
- 会继承的css属性
  - 字体、文本（除了vertical-align）、文字颜色等
- 不会继承的css属性
  - 边框、背景、内边距、外边距、宽高、溢出方式等
- **能继承的属性，都是不影响布局的，简单的说：都是和盒子模型没关系的**

##### 1.3.优先级

- !important > 行内样式 > ID选择器 > 类选择器 > 元素选择器 > *（通配符） > 继承的样式

#### 2.样式

##### 2.1.行内样式

- 属性的值不能随便写，要符合CSS的语法规范，"名：值；"
- 只能控制当前标签的样式，对其他标签无效
- 优先级最高

  ```css
    <h1 style="color:red;font-size:60px;">行内样式</h1>
  ```

##### 2.2.内部样式

- 写在html页面内部，单独放在\<style>标签中
- \<style>标签理论上可以放在HTML文档的任何地方，但一般都放在\<head>标签中
- 样式可以重复利用，代码结构清晰
- 优先级与外部样式相同

```css
    <style>
        h1 {
            color: red;
            font-size: 60px;
        }
    </style>
```

##### 2.3.外部样式

- 写在单独的 .css 文件中，在HTML文档内引入使用
- \<link>标签必须写在\<head>标签内
- \<link>标签属性
  - href：引入的文档来自哪里
  - rel：引入文档与当前文档之间的关系
- 优先级与内部样式相同

#### 3.CSS语法规范

- 选择器：找到要添加样式的元素
- 声明块：设置具体的样式；  ”属性名：属性值“
![alt text](image.png)
- css3基础语法
  - 新增长度单位
    - rem 根元素字体大小的倍数，至于根元素字体大小有关
    - vw 视口宽度的百分之多少，10vw就是视口宽度的10%
    - vh 视口高度的百分之多少，10vh就是视口高度的10%
    - vmax 视口宽高中的大的哪个的百分之多少
    - vmin 视口宽高中小的哪个的百分之多少

#### 4.选择器

##### 4.1.基本选择器

- 通配选择器
  - 选中所有的HTML元素

  ```css
  * {color:red}
  ```

- 元素选择器
  - 为页面中某种元素统一设置样式

  ```css
  h1 {color:red}
  ```

- 类选择器
  - 根据元素的 class 值，来选中某些元素
  - 元素的class属性值不带.   ，但css的类选择器要带.
  - class值是我们自定义的，按照标准：不要使用纯数字；不要使用中文；尽量使用英文+数字的组合；若由多个单词组成，使用 - 连接，例如：left-menu
  - 一个元素不能写多个class关键字，但是可以写多个值，使用空格隔开

  ```css
    .say {color:red}

    <h1 class="speak big">你好啊</h1>
  ```

- id配选择器
  - 根据元素的id属性值，来精准选中某个元素
  - 元素的id属性值不带#   ，但css的id选择器要带#
  - id 属性值：尽量由字母、数字、下划线(_)、短杠(-)组成，最好以字母开头、不要包含空 格、区分大小写
  - 一个元素只能拥有一个id 属性，多个元素的id属性值不能相同
  - 一个元素可以同时拥有id和class属性

  ```css
    #earthy {color:red}
  ```

##### 4.2.复合选择器

- 元素之间的关系
  - 父元素：直接包裹某个元素的元素，就是该元素的父元素
  - 子元素：被父元素直接包含的元素
  - 祖元素：父亲的父亲......，一直往外找，都是祖先
  - 后代元素：儿子的儿子......，一直往里找，都是后代
  - 兄弟元素：具有相同父元素的元素，互为兄弟元素

- ##### 4.2.1.交集选择器

  - 选中同时符合多个条件的元素
  - 有标签名，标签名必须写在最前面
  - 不可能同时出现2个元素选择器
  - 用的最多的是：元素选择器配合类名选择器

  ```css
  # 语法：
  选择器1选择器2选择器3选择器4
  /* 选中：类名为beauty的p元素，为此种写法用的非常多！！！！ */
    p.beauty {
    color: blue;
    }
    /* 选中：类名包含rich和beauty的元素 */
    .rich.beauty {
    color: green;
    }
  ```

- ##### 4.2.2.并集选择器

  - 选中多个选择器对应的元素，又称：分组选择器
  - 多个选择器通过,连接，此处,的含义就是：或
  - 并集选择器，通常用于集体声明，可以缩小样式表体积

  ```css
    # 语法：使用逗号隔开
    选择器1,选择器2,选择器3,选择器4
    /* 选中id为peiqi，或类名为rich，或类名为beauty的元素 */
    #peiqi,
    .rich,
    .beauty {
        font-size: 40px;
        background-color: skyblue;
        width: 200px;
    }
  ```

- ##### 4.2.2.后代选择器

  - 选中指定元素中，符合要求的后代元素
  - 嵌套层级：无限深

  ```css
    # 语法，使用空格隔开
    /* 选中ul中的所有li */
    ul li {
    color: red;  
    }
    /* 选中ul中所有li中的a */
    ul li a {
    color: orange;
    }
    /* 选中类名为subject元素中的所有li */
    .subject li {
    color: blue;
    }
  ```

- ##### 4.2.3.子代选择器

  - 选中指定元素中，符合要求的子元素（儿子元素）。（先写父，再写子）
  - 嵌套层级：仅一级

  ```css
    # 语法，使用 > 隔开
    /* div中的子代a元素 */
    div>a {
    color: red;
    }
    /* 类名为persons的元素中的子代a元素 */
    .persons>a {
    color: red;
    }
  ```

- ##### 4.2.4.兄弟选择器

  - **相邻兄弟选择器**：选中指定元素后，符合条件的相邻兄弟元素

  ```css
    # 语法，使用 + 隔开
    /* 选中div后相邻的兄弟p元素 */
    div+p {
        color:red;
    }
  ```

  - **通用兄弟选择器**：选中指定元素后，符合条件的所有兄弟元素

  ```css
    # 语法，使用 ~ 隔开
    /* 选中div后相邻的兄弟p元素 */
    div~p {
        color:red;
    }
  ```

- ##### 4.2.5.属性选择器

  - 选中属性值符合一定要求的元素
    - [属性名] 选中具有某个属性的元素
    - [属性名="值"] 选中包含某个属性，且属性值等于指定值的元素
    - [属性名^="值"] 选中包含某个属性，且属性值以指定的值开头的元素
    - [属性名$="值"] 选中包含某个属性，且属性值以指定的值结尾的元素
    - [属性名*=“值”] 选择包含某个属性，属性值包含指定值的元素

  ```css
    /* 选中具有title属性的元素 */
    div[title]{color:red;}
    /* 选中title属性值为atguigu的元素 */
    div[title="atguigu"]{color:red;}
    /* 选中title属性值以a开头的元素 */
    div[title^="a"]{color:red;}
    /* 选中title属性值以u结尾的元素 */
    div[title$="u"]{color:red;}
    /* 选中title属性值包含g的元素 */
    div[title*="g"]{color:red;}
  ```

- ##### 4.2.6.伪类选择器

  - **一、动态伪类：**
    - :link 超链接未被访问的状态
    - :visited 超链接访问过的状态
    - :hover 鼠标悬停在元素上的状态
    - :active 元素激活的状态
      - 遵循LVHA 的顺序，即：link 、visited 、hover 、active
    - :focus 获取焦点的元素
      - 当用户：点击元素、触摸元素、或通过键盘的 “tab"键方式，选择元素时就是获得焦点
  - **二、结构伪类：**
    - :first-child 所有兄弟元素中的**第一个**
    - :last-child 所有兄弟元素中的**最后一个**
    - :nth-child(n) 所有兄弟元素中的**第 n 个**
    - :first-of-type 所有**同类型**兄弟元素中的**第一个**
    - :last-of-type 所有**同类型**兄弟元素中的**最后一个**
    - :nth-of-type(n) 所有**同类型**兄弟元素中的 **第n个**
    - :nth-last-child(n) 所有兄弟元素中的**倒数第n个**
    - :nth-last-of-type(n) 所有**同类型**兄弟元素中的 **倒数第n个**
    - :only-child 选择没有兄弟的元素（独生子女）
    - :only-of-type 选择没有**同类型**兄弟的元素
    - :root 根元素
    - :empty 内容为空元素（空格也算内容）
    - **关于n的值：**
      - 0或不写：什么都选不中 —— 几乎不用
      - n：选中所有子元素 —— 几乎不用
      - 1~正无穷的整数 ：选中对应序号的子元素
      - 2n 或 even ：选中序号为偶数的子元素
      - 2n+1 或 odd ：选中序号为奇数的子元素
      - -n+3 ：选中的是前3个
  - **三、否定伪类：**
    - :not(选择器) 排除满足括号中条件的元素
  - **四、UI伪类：**
    - :checked 被选中的复选框或单选按钮
    - :enable 可用的表单元素（没有disabled 属性）
    - :disabled 不可用的表单元素（有disabled 属性）
  - **五、目标伪类（了解）**
    - :target 选中锚点指向的元素
  - **六、语言伪类（了解）**
    - :lang() 根据指定的语言选择元素（本质是看lang 属性的值）

- ##### 4.2.7.伪元素选择器

  - 选中元素中的一些特殊位置
    - ::first-letter 选中元素中的第一个文字
    - ::first-line 选中元素中的第一行文字
    - ::selection 选中被鼠标选中的内容
    - ::placeholder 选中输入框的提示文字
    - ::before 在元素最开始的位置，创建一个子元素（必须用content 属性指定内容）
    - ::after 在元素最后的位置，创建一个子元素（必须用content 属性指定内容）

##### 4.3.选择器的优先级（权重）

- !important > 行内样式 > ID选择器 > 类选择器 > 元素选择器 > 通配选择器
- 计算方式：每个选择器，都可计算出一组权重，格式为：(a,b,c)
  - a : ID 选择器的个数
  - b : 类、伪类、属性 选择器的个数
  - c : 元素、伪元素 选择器的个数
  ![alt text](image-1.png)
  ![alt text](image-2.png)
- **行内样式权重大于所有选择器**
- **!important 的权重，大于行内样式，大于所有选择器，权重最高**

#### 5.常用属性

##### 5.1.颜色

- 颜色名：红色：red/绿色：green/蓝色：blue...
- rgb或rgba
  - r表示红色
  - g表示绿色
  - b表示蓝色
  - a表示透明度

  ```
    /* 使用 0~255 之间的数字表示一种颜色 */
    color: rgb(255, 0, 0);/* 红色 */
    color: rgb(0, 255, 0);/* 绿色 */
    color: rgb(0, 0, 255);/* 蓝色 */
    color: rgb(0, 0, 0);/* 黑色 */
    color: rgb(255, 255, 255);/* 白色 */
    /* 混合出任意一种颜色 */
    color:rgb(138, 43, 226) /* 紫罗兰色 */
    color:rgba(255, 0, 0, 0.5);/* 半透明的红色 */
    /* 也可以使用百分比表示一种颜色（用的少） */
    color: rgb(100%, 0%, 0%);/* 红色 */
    color: rgba(100%, 0%, 0%,50%);/* 半透明的红色 */
  ```

- HEX/HEXA:
  - 原理和RGB差不多
  - 格式: #rrggbb

  ```
    color: #ff0000;/* 红色 */
    color: #00ff00;/* 绿色 */
    color: #0000ff;/* 蓝色 */
    color: #000000;/* 黑色 */
    color: #ffffff;/* 白色 */
    /* 如果每种颜色的两位都是相同的，就可以简写*/
    color: #ff9988;/* 可简为：#f98 */
    /* 但要注意前三位简写了，那么透明度就也要简写 */
    color: #ff998866;/* 可简为：#f986 */
  ```

- HSL/HSLA
  - HSL 是通过：色相、饱和度、亮度，来表示一个颜色的，格式为: hsl(色相,饱和度,亮度)
  - 色相：取值范围是0~360 度，具体度数对应的颜色
  - 饱和度：取值范围是0%~100%(0%是全灰，100%没有灰色)
  - 亮度：取值范围是0%~100%（0%是没有亮度，是黑色。100%是全亮，是白色）

##### 5.2.字体

- 字体大小：font-size
  - Chrome浏览器支持最小文字为12px，默认大小16px，设置0px会自动消失
  - 不同浏览器默认字体大小可能不一致
  - 通常给body设置font-size属性，这样其他元素全都继承了
  - 由于字体设计原因，文字最终呈现的大小，并不一定与font-size的值一致，会有一点误差
  - 通常情况下，文字相对字体设计框，并不是垂直居中的，通常靠下一些
- 字体族：font-family
  - 控制字体类型
  - 使用字体的英文名字兼容性更好
  - 如果字体名包含空格，必须使用引号包裹起来
  - 可以设置多个字体，会按照从左到右的顺序逐个查找，找到就用，通常最后写上serif（衬线字体）或sans-serif（非衬线字体）
  - windows系统默认微软雅黑
- 字体风格：font-style
  - 控制字体是否为斜体
    - normal：正常
    - italic：斜体，字体自带斜体效果。**推荐使用**
    - oblique：斜体，强制倾斜产生斜体效果
- 字体粗细：font-weight
  - 控制字体的粗细
  - 关键字
    - lighter：细
    - normal：正常
    - bold：粗
    - bolder：很粗（多数字体不支持）
  - 数值
    - 100~1000 且无单位，数值越大，字体越粗
    - 100~300等同于lighter，400~500等同于normal，600及以上等同于bold
- 字体复合写法：font
  - 上述字体样式合并成一个属性
  - 字体大小/字体族必须都写上
  - 字体族必须是最后一位，字体大小必须是倒数第二位
  - 各个属性之间用空格隔开

##### 5.3.文本

- 文本颜色：color
  - 控制文本颜色，同5.1
- 文本间距：
  - 字母间距：letter-spacing
  - 单词间距：word-spacing（通过空格识别）
  - 单位px
- 文本修饰：text-decoration
  - 控制文本各种装饰线
    - none：无装饰线
    - underline：下划线
    - overline：上划线
    - line-through：删除线
    - dotted：虚线
    - wavy：波浪线
- 文本缩进：text-indent
  - 控制文本首字母的缩进
  - 属性值：css中的长度单位，例如：px  em等
- 文本对齐-水平：text-align
  - 控制文本的水平对齐方式
    - left：左对齐
    - right：右对齐
    - center：居中对齐
- 行高：line-height
  - 控制一行文字的高度
    - normal：默认值
    - 像素：px
    - 数字：参考font-size倍数
    - 百分比：参考font-size百分比

    ```css
    div { 
        line-height: 60px;
        line-height: 1.5;
        line-height: 150%;
    }
    ```

  - line-height 是可以继承的
  - line-height与height的关系
    - 设置的height那么高度就是height的值
    - 没设置height就会根据line-height计算高度
    - 设置height等于line-height可以实现文字垂直居中
- 文本对齐-垂直：vertical-align
  - 指定**同一行元素之间**，或**表格单元格**内文字的**垂直对齐方式**
    - baseline：默认值
    - top：是元素**顶部**与其**所在行的顶部**对齐
    - middle：是元素的**中部**与**父元素的基线**加上**父元素字母x的一半**对齐
    - bottom：使元素的**底部**预期**所在行的底部**对齐
    - **不能控制块元素**
- 文本阴影：text-shadow
  - 控制文本的阴影
  - 语法：text-shadow: 水平偏移 垂直偏移 模糊半径 颜色;
    - h-shadow：水平偏移
    - v-shadow：垂直偏移
    - blur：模糊半径
    - color：阴影颜色
  - 水平偏移：正值向右，负值向左
  - 垂直偏移：正值向下，负值向上
  - 模糊半径：阴影的模糊程度
  - 颜色：阴影的颜色
- 文本换行：white-space
  - 控制文本的换行方式
    - normal：默认值，自动换行
    - nowrap：不换行
    - pre：保留空格和换行
    - pre-wrap：保留空格和换行，但会自动换行
    - pre-line：合并空格，但保留换行
    - break-all：强制换行
    - keep-all：强制不换行
- 文本溢出：text-overflow
  - 控制文本溢出时的显示方式
    - clip：默认值，隐藏溢出部分
    - ellipsis：显示省略号
  - overflow必须为非visible
  - white-space为nowrap
  - 必须设置宽度
- 文本装饰：text-decoration
  - text-decoration:text-decoration-line || text-decoration-color || text-decoration-style;
  - text-decoration-line:
    - none：无装饰线
    - underline：下划线
    - overline：上划线
    - line-through：删除线
    - dotted：虚线
    - wavy：波浪线
  - text-decoration-color:
    - 颜色
  - text-decoration-style:
    - solid：实线
    - double：双实线
    - dotted：点线
    - dashed：虚线
    - wavy：波浪线

##### 5.4.列表属性：ul、ol、li

- list-style-type：设置列表符号
  - none：不显示前面的标识
  - square：实心方块
  - disc：圆形
  - decimal：数字
  - lower-roman：小写罗马字
  - upper-roman：大写罗马字
  - lower-alpha：小写字母
  - upper-alpha：小写字母
- list-style-position：设置列表符号位置
  - inside：在li的里面
  - outside：在li的外面
- list-style-image：自定义列表符号
  - url 图片地址
- list-style：复合属性
  - 没有数量，顺序要求

##### 5.5.表格属性：table

- 边框
  - border-width：边框宽度
  - border-color：边框颜色
  - border-style：边框风格
    - none：默认
    - solid：实线
    - dashed：虚线
    - dotted：点线
    - double：双实线
  - border：复合属性：没有数量没有顺序要求
- 表格独有属性
  - background-color：设置背景颜色
  - background-image：设置背景图片，url(图片地址)
  - background-repeat：设置背景重复方式
    - repeat：重复铺满整个元素，默认值
    - repeat-x：只在水平方向重复
    - repeat-y：只在垂直方向重复
    - no-repeat：不重复
  - background-position：设置背景图位置
    - 写两个值，用空格隔开
    - 关键字设置位置：
      - 水平：left/center/right
      - 垂直：top/center/bottom
      - 如果只写一个值，另外一个默认center
    - 长度指定坐标位置
      - 以元素左上角为坐标原点，设置图片左上角的位置
      - 两个值，分别是x坐标，y坐标
      - 只写一个值会当作x坐标，y坐标取center
- background：复合属性
  - 没有数量和顺序要求

##### 5.6.CSS鼠标属性：cursor

- 设置光标的样子
  - pointer：小手
  - move：移动图标
  - text：文字选择器
  - crosshairs：十字架
  - wait：等待
  - help：帮助

##### 5.7.背景属性:background

- background-color：设置背景颜色
- background-image：设置背景图片，url(图片地址)
- background-repeat：设置背景重复方式
  - repeat：重复铺满整个元素，默认值
  - repeat-x：只在水平方向重复
  - repeat-y：只在垂直方向重复
  - no-repeat：不重复
- background-position：设置背景图位置
  - 写两个值，用空格隔开
- background-origin：设置背景图片的定位原点
  - border-box：从border区域开始显示背景图片 --默认值
  - padding-box：从padding区域开始显示背景图片
  - content-box：从content区域开始显示背景图片
- background-clip：设置背景图片的裁剪区域
  - border-box：从border区域开始裁剪背景图片 --默认值
  - padding-box：从padding区域开始裁剪背景图片
  - content-box：从content区域开始裁剪背景图片
  - text: 背景只呈现在文字上
- background-size：设置背景图片的大小
  - auto：默认值，按照图片原始大小
  - cover：覆盖整个容器，图片可能会被拉伸
  - contain：包含整个容器，图片可能会有空白
- background-attachment：设置背景图片是否随滚动条滚动
  - scroll：默认值，背景图片随滚动条滚动
  - fixed：背景图片固定在浏览器窗口中
- background：复合属性
  - 没有数量和顺序要求
- 多背景图
![alt text](image-14.png)

##### 5.8.web字体

- 可以通过@font-face 指定字体的具体地址，浏览器会自动下载该字体，这样就不依赖用户电脑上的字体
- @font-face {
    font-family: "字体名称";
    src: url("字体文件路径");
  }
- font-family：字体名称
- src：字体文件路径
- 字体文件格式
  - .ttf：TrueType字体
  - .otf：OpenType字体
  - .woff：Web Open Font Format字体
  - .woff2：Web Open Font Format字体


#### 6.**盒模型**

##### 6.1.CSS长度单位

- px：像素
- em：相对元素font-size的倍数
- rem：相对根字体大小（html标签就是根）
- %：相对父元素计算

##### 6.2.元素显示模式

- 通过CSS中的display属性修改默认显示模式
  - none：元素会被隐藏
  - block：元素将作为块级元素显示
  - inline：元素将作为行级元素显示
  - inline-block：元素将作为行内块级元素显示
- 块元素：block
  - 在页面中**独占一行**，不会与任何元素公用一行，是从上往下排列
  - 默认宽度：撑满**父元素**
  - 默认高度：由**内容**撑开
  - **可以**通过css设置宽高
  - 主题结构标签：
    - \<html> 、 \<body>
  - 排版标签：
    - \<h1> ~ \<h6>、\<hr>、\<p>、\<pre>、\<div>
  - 列表标签：
    - \<ul>、\<ol>、\<li>、\<dl>、\<dt>、\<dd>
  - 表格相关标签：
    - \<table>、\<tbody>、\<thead>、\<tfoot>、\<tr>、\<caption>
- 行内元素：inline
  - 又称：内联元素
  - 在页面中**不独占一行**，一行中不能容纳下的行内元素，会在下一行继续从左到右排列
  - 默认宽度：由内容撑开
  - 默认高度：由内容撑开
  - **无法**通过css设置宽高
  - 文本标签：
    - \<br>、\<em>、\<strong>、\<sup>、\<sub>、\<del>、\<ins>
    - \<a>、\<label>
- 行内块元素：inline-block
  - 内联块元素
  - 在页面中**不独占一行**，一行中不能容纳下的行内元素，会在下一行继续从左到右排列
  - 默认宽度：由内容撑开
  - 默认高度：由内容撑开
  - **可以**通过css设置宽高
  - 图片：\<img>
  - 单元格：\<td>、\<th>
  - 表单控件：\<input>、\<textarea>、\<select>、\<button>
  - 框架标签：\<iframe>

##### 6.3.盒模型的组成

- css会把所有html元素都看成一个盒子，所有样式也都是基于这个盒子
- 盒子的大小 = content +  左右padding +  左右border
- 默认宽度：不设置width时显示的宽度
  - 总宽度 = 父content - 自身左右margin
  - 内容区的宽度 = 父content - 自身左右margin - 自身border - 自身左右padding
![alt text](image-3.png)
- margin（外边距）：盒子与外界的距离，**外边距不会影响盒子的大小，但是会影响盒子的位置**
  - 子元素的margin，是参考父元素的content计算的（因为父元素的content中承装着子元素）
  - 上、左：影响自己的位置；下、右、影响后面兄弟元素的位置
  - 块级元素、行内块元素可以完美设置4方向的margin；但是行内元素只可以设置左右，上下设置无效
  - margin可以为auto，如果给一个块级元素设置左右margin为auto，该块级元素会在父元素中水平居中
  - margin可以是负值
  - **margin塌陷**
    - 第一个子元素的margin-top会作用在父元素上，最后一个子元素的margin-bottom会作用在父元素上
    - **解决方案**
      - 给父元素设置不为0的padding，会影响父容器整体大小
      - 给父元素设置宽度不为0的border，会影响父容器整体大小
      - 给父元素css样式设置 overflow：hidden
  - **margin合并**
    - 上面兄弟元素的下外边距和下面兄弟元素的上外边距会合并，取一个最大值，而不是相加
    - **解决方案**：设置一个上下外边距就可以
![alt text](image-4.png)
- border（边框）：盒子的边框
  ![alt text](image.png)
  - border-radius：设置边框圆角
    - 同时设置四个角的圆角
      - border-radius：10px；4个角都是10px
    - 分开设置每个角的圆角（几乎不用）
      - border-top-left-radius：左上角圆角
      - border-top-right-radius：右上角圆角
      - border-bottom-right-radius：右下角圆角
      - border-bottom-left-radius：左下角圆角
    - 分开设置每个角的圆角综合写法（几乎不用）
      - border-radius：10px 20px 30px 40px；左上角10px，右上角20px，右下角30px，左下角40px

- padding（内边距）：津贴内容的补白区域
  - padding的值不能为负数
  - padding-top：上内边距
  - padding-right：右内边距
  - padding-bottom：下内边距
  - padding-left：左内边距
  - padding：复合属性
    - padding：10px；4个方向内边距全是10px
    - padding：10px 20px；上下10px，左右20px
    - padding：10px 20px 30px；上10px，左右20px，下30px
    - padding：10px 20px 30px 40px；上10px，右20px，下30px，左40px
- content（内容）：元素中的文本或后代元素都是它的内容
  - width：设置内容区宽度
  - max-width：设置内容区的最大宽度
  - min-width：设置内容区的最小宽度
  - height：设置内容区的高度
  - max-height：设置内容区的最大高度
  - min-height：设置内容区的最小高度

##### 6.4.处理内容溢出

- overflow：溢出内容的处理方式
  - visible：默认，显示
  - hidden：隐藏(常用)
  - scroll：显示滚动条
  - auto：自动显示滚动条，内容不溢出不显示(常用)
- overflow-x：同上
- overflow-y：同上

##### 6.5.隐藏元素的方式

- visibility：
  - show：默认
  - hidden：隐藏，元素看不见但是也还会占有原来位置
- display
  - none：隐藏，彻底看不见，也不占用位置

##### 6.6.布局小技巧

- 行内元素、行内块元素，可以被父元素当作文本处理
- 如何让子元素在父元素中水平居中
  - 若子元素为块元素，给父元素加上margin：0 auto
  - 若子元素为行内元素、行内块元素，给父元素加上：text-align：center
- 如何让子元素在父元素中垂直居中
  - 若子元素为块元素，给子元素加上margin-top，值为（父元素 - 子元素盒子总高）/2
  - 若子元素为行内元素、行内块元素：让父元素的 height=line-height，每个子元素都加上vertical-align：middle
  - 若想绝对垂直居中，父元素font-size设置为0

##### 6.7.元素之间的空白问题

- 产生原因：行内元素、行内块元素，彼此之间的换行会被浏览器解析为一个空白字符
  - 解决方案：
    - 去掉换行和空格
    - 给父元素设置font-size：0，再给需要显示文字的元素单独设置字体大小

##### 6.8.行内块的幽灵空白问题

- 产生原因：行内块元素与文本的基线对齐，而文本的基线与文本最低端之间有一定距离
  - 解决方案：
    - 给行内块元素设置vertical，值不为baseline即可，设置为middle、buttom、top均可
    - 若父元素中只有一张图片，设置图片为display：block
    - 给父元素设置font-size：0，如果该行内块内部还有文本，则需要单独设置font-size

##### 6.9 新增和模型相关属性

- box-sizing：
  - content-box：标准盒模型
    - 场景内，如果设置了宽高，那么如果有border和padding，那么内容区的大小不变，整个盒子的宽高会撑开，
    - 设置的width、height只包括内容区的大小
    - 但是不包括margin
  - border-box：怪异盒模型
    - 场景内，如果设置了宽高，那么如果有border和padding，那么内容区的大小会自动缩小
    - 设置的width、height就包括了border和padding
    - 但是不包括margin
- resize：
  - none：默认，不能调整大小
  - both：可以拖动调整大小
  - horizontal：只能水平方向调整大小
  - vertical：只能垂直方向调整大小
  - 必须设置overflow：auto，否则无法调整大小
- box-shadow：
  - none：默认，无阴影
  - h-shadow：阴影水平偏移量
  - v-shadow：阴影垂直偏移量
  - blur：模糊距离
  - spread：阴影的扩散半径
  - color：阴影颜色
  - inset：阴影在元素内部
![alt text](image-15.png)
![alt text](image-16.png)
- opacity：
  - 0：完全透明
  - 1：完全不透明
  - 0.5：半透明


#### 7.**浮动**

- 最初浮动是用来实现文字环绕图片效果的，现在浮动用来左页面布局
  ![alt text](image-5.png)

##### 7.1.元素浮动后的特点

- 不管浮动前是什么元素，浮动后：默认宽与高都是被内容撑开（尽可能小），而且可以设置宽高
- 不会独占一行，可以与其他元素共用一行
- 不会与margin合并，也不会引起margin塌陷，能够完美设置四方向的margin和padding
- 不会像行内块元素一样被当作文本处理

##### 7.2.元素浮动后的影响

- 影响
  - 对兄弟元素的影响：后面的兄弟元素，会占用浮动元素之前的位置，在浮动元素的下；对前面兄弟无影响
  - 对父元素的影响：不能撑起父元素的高度，导致父元素高度塌陷；但父元素的官渡依然束缚浮动的元素
- 解决影响
  - 给父元素指定高度
  - 给父元素也设置浮动
  - 给父元素设置：overflow：hidden
  - 在所有浮动元素的最后面，添加一个块级元素，并且给该块级元素设置 clear：both
  - 给浮动元素的父元素，设置成伪元素，通过伪元素清除浮动，原理与上一个相同

#### 8.定位

- 相对定位(position:relative)
  - 相对定位参考点：相对自己原来的位置
  - 可以使用left、right、top、bottom四个属性 调整位置
  - left和right不能同时设置，top和bottom不能同时设置
  - 不会脱离文档流，元素位置的变化不会影响到其他元素，只是视觉上的变化
  - 定位元素的显示层级比普通元素高
    - 定位的元素会盖在普通元素中之上
    - 都发生定位的两个元素，后写的元素会盖在先写的元素上面
  - 相对定位的元素也能继续浮动，但是不推荐
  - 相对定位的元素也能通过margin调整位置，但不推荐这么做
  - 应用场景：对位置微调，并且不脱离文档流，一般会和**绝对定位**配合使用
- 绝对定位(position:absolute)
  - 定位参考点：包含块
    - 对于没有脱离文档流的元素：包含块就是父元素
    - 对于脱离文档流的元素：包含块是第一个拥有定位属性的祖先元素，如果所有祖先都没有定位，那么包含块就是整个页面
  - 脱离文档流，会对后面兄弟元素、父元素都有影响
  - 可以使用left、right、top、bottom四个属性 调整位置
  - left和right不能同时设置，top和bottom不能同时设置
  - 绝对定位、浮动不能同时设置，如果同时设置，浮动失效，以定位为主
  - 绝对定位元素，也能通过margin调整位置，但不推荐
  - 无论是什么元素（行内，行内块，块级）设置为绝对定位之后，都变成了定位元素
    - 定位元素：默认宽高都被内容所撑开，并且可以自由设置宽高
- 固定定位（position: fixed）（广告）
  - 固定定位的参考点：视口（相对于PC浏览器来说）
  - 可以使用left、right、top、bottom四个属性 调整位置
  - left和right不能同时设置，top和bottom不能同时设置
  - 脱离文档流，会对后面的兄弟元素、父元素有影响
  - 固定定位元素也能通过margin调整位置，但不推荐
  - 无论是什么元素（行内，行内块，块级）设置为固定定位之后，都变成了定位元素
- 粘性定位（position：sticky）
  - 粘性定位参考点：离它最近的一个拥有滚动机制的祖先元素，即便这个祖先不是最近的真实可滚动祖先
  - 可以使用left、right、top、bottom四个属性 调整位置，最常用的是top
  - 不会脱离文档流，它是一种专门用于窗口滚动时的新的定位方式
  - 粘性定位和浮动可以同时设置，但不推荐这样做
  - 粘性定位的元素，也能通过margin调整位置，但不推荐这样做
- 定位层级
  - 通过css属性 z-index 调整元素显示层级：z-index的属性值是数字，没有单位，数值越大显示层级越高
  - 如果发生位置重叠，默认的是：后面的元素会显示在前面的元素之上
  - 只有定位的元素设置z-index才有效
- 定位特殊应用
  - 当父元素开启相对定位以后（postion:relative），并且父元素有宽高。
    - 子元素开启绝对定位（postion:absolute）
      - 子元素如果没有设置宽高，那么设置left、right、top、bottom四个属性都为0，那么子元素会直接铺满整个父元素
      - 子元素如果设置了宽高，那么设置left、right、top、bottom四个属性为0，并且设置margin:auto，那么子元素会在父元素中居中

#### 9.布局

##### 9.1.版心

- 在PC端网页中，一般都会有一个固定狂爱且水平居中的盒子，来显示网页的主要内容，这是网页的版心
- 版心的宽度一般是960~1200像素之间
- 版心可以是一个，也可以是多个

##### 9.2.常用布局的名词

![alt text](image-6.png)

##### 9.3.重置默认样式

![alt text](image-7.png)

- 方案一：使用全局选择器

  ```css
  * {
    margin: 0;
    padding: 0;
    ......
  }
  ```

- 方案二：reset.css
  - 选择到具有默认样式的元素，清空其默认的样式
- 方案三：Normailze.css
  - Normalize.css 是一种最新方案，它在清除默认样式的基础上，保留了一些有价值的默认样式
    - 官网地址：<http://necolas.github.io/normalize.css/>
  - 相对方案一和方案二有以下优点
    - 保护了有价值的默认样式，而不是完全去掉它们
    - 为大部分HTML元素提供一般化的样式
    - 新增HTML5元素的设置
    - 对并集选择器的使用比较谨慎，有效避免调试工具杂乱

#### 10.渐变

- 线性渐变
  - 多个颜色之间的渐变， 默认从上到下渐变。
  - 语法：
    - background-image: linear-gradient(direction, color-stop1, color-stop2, ...);
    - direction：渐变的方向，可以是角度，也可以是关键词
    - color-stop1、color-stop2：渐变的颜色，可以设置多个
  - 例子：
    - background-image: linear-gradient(to right, red, blue);

#### 11.变换：transform
- 2D变换：
  - 位移
    - translateX: 移动元素，x为水平方向，参考自身的坐标
    - translateY: 移动元素，y为垂直方向，参考自身的坐标
    - translate(x, y)：移动元素，x为水平方向，y为垂直方向；参考自身的坐标
    - 位移与相对定位类似，都不脱离文档流，不会影响到其 他元素
    - 与相对定于相比：相对定位的百分比值，参考的是父元素；位移的百分比值，参考的是自身元素
    - 浏览器针对位移有优化
    - 位移对行内元素无效
    - 位移配合定位可实现元素水平垂直居中
    - ![alt text](image-17.png)
  - 旋转
    - rotate(angle)：旋转元素，angle为旋转角度，正数为顺时针，负数为逆时针；相当于rotateZ
  - 缩放
    - scale(x, y)：缩放元素，x为水平方向，y为垂直方向
    - 对行内元素无效
  - 倾斜
    - skew(x, y)：倾斜元素，x为水平方向，y为垂直方向
  - 变换原点
    - transform-origin：设置元素的变换原点
    - 语法：transform-origin: x y;
    - x：水平方向，可以是像素值、百分比值、left、center、right
    - y：垂直方向，可以是像素值、百分比值、top、center、bottom

#### 12.过渡：transition
- 过渡可以在不使用 Flash 动画，不使用 JavaScript 的情况下，让元素从一种样式，平滑过渡为另一种样式。
##### 12.1. transition-property
  - transition-property：设置过渡的属性
  - 语法：transition-property: property;
  - property：可以是单个属性，也可以是多个属性，多个属性用逗号分隔
  - all：所有属性
  - none：没有属性
  - 具体某个属性名，例如：width 、heigth ，若有多个以逗号分隔。只要是数值相关的属性都可以设置；常用属性有：颜色、长度值、百分比、z-index、opacity、2D变换属性、3D变换属性、阴影
##### 12.2. transition-duration
  - transition-duration：设置过渡的时间
  - 语法：transition-duration: time;
  - time：可以是秒(s)、毫秒(ms)
  - 默认值是0，表示没有过渡效果
  - 如果想让多个属性同时设置过渡时间，可以使用逗号分隔
  - 如果想让所有属性都持续相同的时间，只写一个值就行
##### 12.3. transition-delay
  - transition-delay：设置过渡的延迟时间
  - 语法：transition-delay: time;
  - time：可以是秒(s)、毫秒(ms)
  - 默认值是0，表示没有延迟
  - 如果想让多个属性同时设置延迟时间，可以使用逗号分隔
  - 如果想让所有属性都延迟相同的时间，只写一个值就行
##### 12.4. transition-timing-function
  - transition-timing-function：设置过渡的速度曲线
  - 语法：transition-timing-function: timing-function;
  - timing-function：可
  - 以是以下值
    - ease：默认值，慢速开始，然后变快，然后慢速结束
    - linear：匀速
    - ease-in：慢速开始
    - ease-out：慢速结束
    - ease-in-out：慢速开始和结束
    - step-start：立即跳到结束状态
    - step-end：在指定时间跳到结束状态
    - steps：分步函数；2个参数：第一个参数是分了几步，第二个参数是第一步的值发生变化的时间点是开始还是结束
    - cubic-bezier(n,n,n,n)：自定义速度曲线
##### 12.5. transition复合属性
  - transition：设置过渡的属性、时间、延迟、速度曲线
  - 语法：transition: property duration delay timing-function;
  - 如果设置了一个时间，表示duration;如果设置了两个时间，第一出现的时间是duration，第二个出现的时间是delay;其他值没有顺序要求

#### 13.动画
- 动画可以在不使用 Flash 动画，不使用 JavaScript 的情况下，让元素从一种样式，平滑过渡为另一种样式。
- 动画是多个状态的集合
- 语法：
  - @keyframes animationname {keyframes-selector {css-styles;}}
  - animationname：动画名称
  - keyframes-selector：动画的阶段，可以是百分比，也可以是关键词
  - css-styles：动画的样式
  - animation-duration：动画的时长
  - animation-timing-function：动画的速度曲线
  - animation-delay：动画的延迟时间
  - animation-iteration-count：动画的播放次数
  - animation-direction：动画的播放方向
  - animation-fill-mode：动画的填充模式
  - animation-play-state：动画的播放状态
  - animation：动画的简写属性

#### 14.多列布局
- 专门用于实现类似于报纸的布局
- 语法：
  - column-count：设置列数
  - column-width：设置列宽
  - column-gap：设置列间距
  - column-rule：设置列之间的边框
  - column-span：设置元素跨越的列数
  - column-fill：设置列的填充方式
  - column-break-before：设置元素之前的分页符
  - column-break-after：设置元素之后的分页符

#### 15.伸缩盒模型（弹性盒子）：display：flex
##### 15.1. 伸缩容器、伸缩项目
- 伸缩容器：CSS设置 display: flex;
  - 一个元素可以是伸缩容器，也可以是伸缩项目
- 伸缩项目：伸缩容器内所有**子元素**自动成为伸缩项目
  - 仅伸缩容器的子元素成为伸缩项目，孙子元素、重孙子元素等后代元素不会成为伸缩项目
  - 无论原来是哪种元素（块、行内、行内块）一旦成为了伸缩项目，全部会成为块状化
##### 15.2. 主轴与侧轴
- 主轴：伸缩项目沿着主轴排列，主轴默认是水平的，默认方向是：从左到右（左边是起点，右边是终点）
  - 主轴方向
    - 属性名：flex-direction
      - 常用属性:
        - row：水平方向，从左到右---默认值
        - row-reverse：水平方向，从右到左
        - column：垂直方向，从上到下
        - column-reverse：垂直方向，从下到上
        - ![alt text](image-18.png)
        - 改变了主轴的方向，侧轴方向也随之改变
  - 主轴换行方式
    - 属性名：flex-wrap
      - 常用属性:
        - nowrap：不换行---默认值
        - wrap：换行，第一行在上边
        - wrap-reverse：换行，第一行在下边
        - ![alt text](image-19.png)
  - flex-flow：复合属性
    - 复合了flex-direction和flex-wrap
    - 语法：flex-flow: flex-direction flex-wrap;
    - 顺序可以颠倒
    - 如果只写一个值，表示flex-direction
  - 主轴对齐方式
    - 属性名：justify-content
      - 常用属性:
        - flex-start：主轴起点对齐---默认值
        - flex-end：主轴终点对齐
        - center：主轴居中对齐
        - space-between：主轴两端对齐，项目之间的间隔相等
        - space-around：每个项目两侧的间隔相等
        - space-evenly：每个项目两侧的间隔相等，项目之间的间隔是项目与项目之间间隔的两倍
        - ![alt text](image-20.png)
- 侧轴：与主轴垂直的就是侧轴，侧轴默认是垂直的，默认方向是：从上到下（上边是起点，下边是终点）
  - 侧轴对齐方式：
    - 单行的情况
      - 属性名：align-items
        - 常用属性:
          - stretch：如果伸缩项目未设置高度，将占满整个容器高度---默认值
          - flex-start：侧轴起点对齐
          - flex-end：侧轴终点对齐
          - center：侧轴居中对齐
          - baseline：伸缩项目的第一行文字的基线对齐
          - ![alt text](image-21.png)
    - 多行的情况
      - 属性名：align-content
        - 常用属性:
          - stretch：如果伸缩项目未设置高度，将占满整个容器高度---默认值
          - flex-start：侧轴起点对齐
          - flex-end：侧轴终点对齐
          - center：侧轴居中对齐
          - space-between：侧轴两端对齐，项目之间的间隔相等
          - space-around：每个项目两侧的间隔相等
          - space-evenly：每个项目两侧的间隔相等，项目之间的间隔是项目与项目之间间隔的两倍
          - ![alt text](image-22.png)
          - ![alt text](image-23.png)
      - 单独对齐：在单独伸缩项目内使用，只调整单独一个项目
        - 属性名：align-self
          - 常用属性:
            - stretch：如果伸缩项目未设置高度，将占满整个容器高度---默认值
            - flex-start：侧轴起点对齐
            - flex-end：侧轴终点对齐
            - center：侧轴居中对齐
            - baseline：伸缩项目的第一行文字的基线对齐
- flex实现水平垂直居中
  - 方法一：父容器开启flex布局，随后使用justify-content和align-items
    - ![alt text](image-24.png)
  - 方法二：父容器开启flex布局，随后使用margin:auto
    - ![alt text](image-25.png)
##### 15.3. 伸缩性
- flex-basis:设置的是主轴方向的基准长度，会让宽度或者高度失效
  - 主轴横向：宽度失效；主轴纵向：高度失效
  - 浏览器根据这个属性的值，计算主轴上是否有多余的控件，默认值为auto
  - 语法：flex-basis: length;
  - length：可以是像素值、百分比值、auto
  - auto：根据伸缩项目的内容计算
- flex-grow:设置的是主轴方向的放大比例
  - 语法：flex-grow: number;
  - number：可以是0、1、2、3等整数
  - 0：不放大，默认为0
  - 若所有伸缩项目的flex-grow值都为1，则：它们将等分父容器未占满的剩余空间
  - 若每个伸缩项目设置不同的值，那么根据设置的值的比例来划分；如果为1、2、3；则瓜分到1/6、2/6、3/6份
- flex-shrink:设置的是主轴方向的缩小比例
  - 语法：flex-shrink: number;
  - number：可以是0、1、2、3等整数
  - 0：不缩小，默认为1
  - flex-shrink计算缩小方式：
    - 1. 计算所有伸缩项目的flex-shrink值的总和
    - 2. 计算每个伸缩项目的flex-shrink值占总和的比例
    - 3. 计算每个伸缩项目的flex-shrink值占总和的比例，乘以父容器的宽度，得到每个伸缩项目的宽度
    - 假设三个收缩项目，宽度分别为200px、300px、200px；flex-shrink值分别为1、2、1；父容器的宽度为500px，flex-shrink值分别为1、2、1
      - 先计算分母：200x1+300x2+200x1=1000
      - 再计算分子比例：200x1/1000=0.2；300x2/1000=0.6；200x1/1000=0.2
      - 再计算每个伸缩项目的宽度：200x0.2=40px；200x0.6=120px；200x0.2=40px；这些值就是每个收缩项目缩小的值
- flex:复合属性
  - 复合了flex-grow、flex-shrink、flex-basis；默认值 0 1 auto
  - ![alt text](image-26.png)

#### 16. 网格布局：display：gird
- **行列属性**
  - grid-template-columns：设置列的宽度
  - grid-template-rows：设置行的高度
  - 数值：
    - ![alt text](image-27.png)
  - 百分比：
    - ![alt text](image-28.png)
  - 重复函数：repeat
    - ![alt text](image-29.png)
  - 自动填充：repeat内的auto-fill
    - ![alt text](image-30.png)
    - ![alt text](image-31.png)
  - auto自动
    - ![alt text](image-32.png)
  - fr片段划分
    - ![alt text](image-33.png)
  - minmax()
    - ![alt text](image-34.png)
- **间距**
  - grid-column-gap：设置列间距
  - grid-row-gap：设置行间距
  - grid-gap：复合属性
    - 复合了grid-column-gap和grid-row-gap
    - 语法：grid-gap: grid-column-gap grid-row-gap;
    - 顺序可以颠倒
    - 如果只写一个值，表示grid-column-gap
- **调整顺序**
  - grid-auto-flow：设置自动布局的方向
    - 语法：grid-auto-flow: row | column;
    - row：自动布局的方向为行
    - column：自动布局的方向为列
- **对齐方式**
  - justify-items：设置格子列的对齐方式
    - start：左对齐
    - end：右对齐
    - center：居中对齐
    - stretch：拉伸对齐
  - align-items：设置格子行的对齐方式
  - place-items：复合属性
    - 复合了justify-items和align-items
    - 语法：place-items: justify-items align-items;
    - 顺序可以颠倒，如果只写一个值，表示justify-items
  - justify-content：设置容器列的对齐方式
    - start：左对齐
    - end：右对齐
    - center：居中对齐
    - stretch：拉伸对齐
  - align-content：设置容器行的对齐方式
  - place-content：复合属性
    - 复合了justify-content和align-content
    - 语法：place-content: justify-content align-content;
    - 顺序可以颠倒，如果只写一个值，表示justify-content
- **合并单元格**
  - grid-column-start：设置格子的起始列
  - grid-column-end：设置格子的结束列
  - grid-column：复合属性
    - 复合了grid-column-start和grid-column-end
    - 语法：grid-column: grid-column-start / grid-column-end;
  - grid-row-start：设置格子的起始行
  - grid-row-end：设置格子的结束行
  - grid-row：复合属性
    - 复合了grid-row-start和grid-row-end
    - 语法：grid-row: grid-row-start / grid-row-end;
  - grid-area：复合属性
    - 复合了grid-column-start、grid-column-end、grid-row-start、grid-row-end
  - ![alt text](image-35.png)


#### 17. BFC:block formatting context,块级格式上下文
- 能解决什么问题
  - 子元素不会再产生margin塌陷问题
  - 自己不会呗其他浮动元素所覆盖
  - 就算其子元素浮动，元素自身高度也不会塌陷
- 如何开启BFC
  - 根元素
  - 浮动元素；float：left/right
  - 绝对定位、固定定位的元素；position：absolute/fixed
  - 行内块元素；display:inline-block
  - 表格单元格：table、thead、tbody、tfoot、th、td、tr、caption
  - overflow不为visible的元素；overflow：hidden/auto
  - 伸缩项目：display:flex;
  - 多列容器：column-count:1
  - column-span为all的元素
  - display: flow-root;
